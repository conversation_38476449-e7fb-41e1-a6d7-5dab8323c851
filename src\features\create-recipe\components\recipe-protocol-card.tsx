/**
 * @fileoverview Detailed recipe protocol card component
 * Shows complete recipe information with collapsible sections
 */

'use client';

import React, { useState } from 'react';
import { RecipeTimeSlot, FinalRecipeProtocol } from '../types/recipe.types';

interface RecipeProtocolCardProps {
  timeSlot: RecipeTimeSlot;
  recipe: FinalRecipeProtocol | null;
}

/**
 * Detailed recipe protocol card with collapsible sections
 * Follows the exact design from standalone-v1.html protocol cards
 */
export function RecipeProtocolCard({ timeSlot, recipe }: RecipeProtocolCardProps) {
  const [openSections, setOpenSections] = useState<Set<string>>(new Set());

  const timeSlotConfig = {
    morning: {
      emoji: '🌅',
      label: 'Protocolo Matinal',
      subtitle: 'Sinergia para Foco e Calma',
      timeRange: '7h - 9h',
      badgeColor: 'bg-teal-100 text-teal-800'
    },
    'mid-day': {
      emoji: '☀️',
      label: 'Protocolo Diurno',
      subtitle: '<PERSON><PERSON><PERSON> imediato da dor',
      timeRange: '12h - 14h',
      badgeColor: 'bg-orange-100 text-orange-800'
    },
    night: {
      emoji: '🌙',
      label: 'Protocolo Noturno',
      subtitle: 'Sono reparador e relaxamento',
      timeRange: '20h - 22h',
      badgeColor: 'bg-indigo-100 text-indigo-800'
    }
  };

  const config = timeSlotConfig[timeSlot];

  const toggleSection = (sectionId: string) => {
    const newOpenSections = new Set(openSections);
    if (newOpenSections.has(sectionId)) {
      newOpenSections.delete(sectionId);
    } else {
      newOpenSections.add(sectionId);
    }
    setOpenSections(newOpenSections);
  };

  if (!recipe) {
    return (
      <div className="w-full max-w-4xl bg-gray-100 rounded-2xl p-8 animate-pulse">
        <div className="text-center text-gray-500">Receita não disponível</div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-4xl bg-white rounded-3xl shadow-2xl overflow-hidden">
      {/* Time Badge */}
      <div className="absolute top-4 right-4 z-10 flex flex-col items-center justify-center gap-1 bg-white/20 backdrop-blur-sm text-white px-4 py-2 rounded-2xl">
        <span className="font-bold">{config.timeRange}</span>
        <span className={`inline-block text-xs font-semibold px-3 py-1 rounded-full ${config.badgeColor}`}>
          Uso Tópico
        </span>
      </div>

      {/* Header */}
      <div className="bg-gradient-to-r from-teal-600 to-teal-700 text-white px-6 py-6 pb-16 relative">
        <div className="text-3xl font-bold mb-2">
          {config.emoji} {config.label}
        </div>
        <div className="text-lg opacity-90">
          {config.subtitle}
        </div>
      </div>

      {/* Recipe Visual Section */}
      <div className="bg-white mx-6 -mt-10 rounded-2xl p-5 shadow-lg relative z-5">
        {/* Droplet Visualizer */}
        <div className="flex justify-center items-end gap-1 h-10 pb-4 mb-2">
          {recipe.selected_oils.map((oil, index) => (
            <div key={index} className="flex gap-1">
              {Array.from({ length: oil.drops_count }).map((_, dropIndex) => (
                <div
                  key={dropIndex}
                  className={`
                    w-2 rounded-full animate-bounce
                    ${oil.name_localized.toLowerCase().includes('lavanda') ? 'bg-purple-400 h-6' :
                      oil.name_localized.toLowerCase().includes('olíbano') ? 'bg-orange-400 h-5' :
                      oil.name_localized.toLowerCase().includes('copaíba') ? 'bg-green-400 h-4' :
                      oil.name_localized.toLowerCase().includes('hortelã') ? 'bg-teal-400 h-5' :
                      'bg-blue-400 h-5'
                    }
                  `}
                  style={{ animationDelay: `${(index * oil.drops_count + dropIndex) * 0.1}s` }}
                />
              ))}
            </div>
          ))}
        </div>

        {/* Quick Info Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 py-4">
          <div className="text-center">
            <div className="text-xs text-gray-500 font-normal uppercase tracking-wide">Total Gotas</div>
            <div className="text-lg font-bold text-gray-800">{recipe.total_drops}</div>
          </div>
          <div className="text-center">
            <div className="text-xs text-gray-500 font-normal uppercase tracking-wide">Diluição</div>
            <div className="text-lg font-bold text-gray-800">
              {Math.round((recipe.total_drops / 20 / recipe.total_volume_ml) * 100 * 10) / 10}%
            </div>
          </div>
          <div className="text-center">
            <div className="text-xs text-gray-500 font-normal uppercase tracking-wide">Tamanho</div>
            <div className="text-lg font-bold text-gray-800">{recipe.container_recommendation.size_ml}ml</div>
          </div>
          <div className="text-center">
            <div className="text-xs text-gray-500 font-normal uppercase tracking-wide">Tampa</div>
            <div className="text-lg font-bold text-gray-800">
              {recipe.container_recommendation.container_type === 'roller_bottle' ? 'Roll-on' : 
               recipe.container_recommendation.container_type === 'dropper_bottle' ? 'Conta-gotas' :
               'Spray'}
            </div>
          </div>
        </div>

        {/* Ingredients Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          {/* Essential Oils */}
          <div>
            <h5 className="text-sm font-semibold text-gray-700 uppercase tracking-wide mb-2">
              Óleos Essenciais
            </h5>
            <div className="space-y-2 border-t pt-2">
              {recipe.selected_oils.map((oil, index) => (
                <div key={index} className="flex items-center gap-3 py-2">
                  <div className="flex-shrink-0 text-center text-xs font-semibold bg-teal-50 text-teal-700 rounded-lg w-16 py-2">
                    {oil.drops_count} gotas
                  </div>
                  <div className="flex-1">
                    <div className="font-semibold text-gray-800">{oil.name_localized}</div>
                    <div className="text-xs italic text-gray-500">{oil.name_botanical}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Carrier Oil */}
          <div>
            <h5 className="text-sm font-semibold text-gray-700 uppercase tracking-wide mb-2">
              Óleo Carreador
            </h5>
            <div className="space-y-2 border-t pt-2">
              <div className="bg-teal-50 p-3 rounded-lg border border-teal-100">
                <p className="font-bold text-teal-900">
                  {recipe.carrier_oil.name_localized} <span className="font-medium text-teal-800">(Recomendado)</span>
                </p>
                <p className="text-xs text-teal-700">
                  Quantidade: {recipe.carrier_oil.amount_ml}ml
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Collapsible Sections */}
      <div className="w-full">
        <CollapsibleSection
          id="usage"
          title="Como Usar"
          subtitle="Modos de aplicação e frequência"
          icon={<ShieldIcon />}
          isOpen={openSections.has('usage')}
          onToggle={() => toggleSection('usage')}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {recipe.usage_instructions_localized.map((instruction, index) => (
              <div key={index} className="bg-slate-50 p-4 rounded-lg text-left">
                <h5 className="font-semibold text-gray-800">{index + 1}. {instruction.split(':')[0]}</h5>
                <p className="text-sm text-gray-600">{instruction.split(':').slice(1).join(':')}</p>
                <span className="inline-block mt-2 text-xs font-medium bg-teal-100 text-teal-800 px-2 py-0.5 rounded-full">
                  {recipe.frequency_localized}
                </span>
              </div>
            ))}
          </div>
        </CollapsibleSection>

        <CollapsibleSection
          id="preparation"
          title="Instruções de Preparo"
          subtitle="Passo a passo da mistura"
          icon={<FlaskIcon />}
          isOpen={openSections.has('preparation')}
          onToggle={() => toggleSection('preparation')}
        >
          <div className="flex flex-col md:flex-row gap-8">
            <div className="md:w-1/3 bg-slate-50 p-6 rounded-xl">
              <h4 className="font-semibold mb-4 text-slate-800">Ingredientes</h4>
              <ul className="space-y-2 text-sm">
                {recipe.selected_oils.map((oil, index) => (
                  <li key={index} className="flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-violet-500"></div>
                    {oil.drops_count} gotas de {oil.name_localized}
                  </li>
                ))}
                <li className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-teal-500"></div>
                  ~{recipe.carrier_oil.amount_ml}ml de {recipe.carrier_oil.name_localized}
                </li>
              </ul>
            </div>
            <div className="md:w-2/3 bg-slate-50 p-6 rounded-xl">
              <h4 className="font-semibold text-slate-800 mb-4">Passos do Preparo</h4>
              <div className="space-y-2">
                {recipe.preparation_steps_localized.map((step, index) => (
                  <div key={index} className="text-sm text-slate-700">
                    <span className="font-bold">{index + 1}.</span> {step}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CollapsibleSection>

        <CollapsibleSection
          id="science"
          title="Como Funciona"
          subtitle="A ciência por trás dos óleos"
          icon={<ScienceIcon />}
          isOpen={openSections.has('science')}
          onToggle={() => toggleSection('science')}
        >
          <div className="space-y-4">
            <div className="mb-4">
              <h4 className="text-lg font-bold text-cyan-900">Racional Científico</h4>
            </div>
            {recipe.selected_oils.map((oil, index) => (
              <div key={index}>
                <h5 className="font-semibold text-gray-800">{oil.name_localized}</h5>
                <p className="text-xs italic text-gray-500 mb-1">{oil.name_botanical}</p>
                <p className="text-sm text-gray-700 mb-2">{oil.rationale_localized}</p>
              </div>
            ))}
          </div>
        </CollapsibleSection>
      </div>
    </div>
  );
}

/**
 * Collapsible section component
 */
interface CollapsibleSectionProps {
  id: string;
  title: string;
  subtitle: string;
  icon: React.ReactNode;
  isOpen: boolean;
  onToggle: () => void;
  children: React.ReactNode;
}

function CollapsibleSection({ title, subtitle, icon, isOpen, onToggle, children }: CollapsibleSectionProps) {
  return (
    <details open={isOpen}>
      <summary
        onClick={(e) => {
          e.preventDefault();
          onToggle();
        }}
        className="list-none cursor-pointer bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-500 text-white px-6 py-4 transition-all duration-300 flex justify-between items-center"
      >
        <div className="flex items-center gap-3">
          <span className="h-6 w-6">{icon}</span>
          <div>
            <div className="font-semibold">{title}</div>
            <div className="text-sm opacity-80">{subtitle}</div>
          </div>
        </div>
        <svg
          className={`w-5 h-5 transition-transform duration-300 ${isOpen ? 'rotate-90' : ''}`}
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
        </svg>
      </summary>
      {isOpen && (
        <div className="bg-gray-50 px-6 py-5">
          {children}
        </div>
      )}
    </details>
  );
}

// ============================================================================
// ICON COMPONENTS
// ============================================================================

function ShieldIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
    </svg>
  );
}

function FlaskIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
    </svg>
  );
}

function ScienceIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
    </svg>
  );
}
