 🔍 [Final Recipes] - selectedSymptomsCount: 2
 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils (NEW): true
final-recipes-display.tsx:407 🔍 [Final Recipes] - suggestedOils.length (OLD/PROBLEMATIC): 0
final-recipes-display.tsx:408 🔍 [Final Recipes] - hasStartedGeneration: false
final-recipes-display.tsx:409 🔍 [Final Recipes] - hasRequiredData (FINAL): true
final-recipes-display.tsx:412 ✅ [Final Recipes] Auto-starting recipe generation with required data (using therapeuticProperties)
final-recipes-display.tsx:107 🍃 [Final Recipes] handleGenerateRecipes called
final-recipes-display.tsx:147 🍃 [Final Recipes] Starting recipe generation process
final-recipes-display.tsx:148 🔍 [Final Recipes] Input data summary: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono', demographics: {…}, selectedCausesCount: 3, selectedSymptomsCount: 2, suggestedOilsCount: 0, …}
final-recipes-display.tsx:162 🔍 [Final Recipes] Selected causes: (3) [{…}, {…}, {…}]
final-recipes-display.tsx:166 🔍 [Final Recipes] Selected symptoms: (2) [{…}, {…}]
final-recipes-display.tsx:170 🔍 [Final Recipes] Therapeutic properties with oils (UPDATED - same as debug overlay): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:186 🔍 [Final Recipes] Creating minimal data structure (same as debug overlay)...
final-recipes-display.tsx:203 🔍 [Final Recipes] Extracted oils from therapeuticProperties: {totalOilsCount: 18, oilsSample: Array(3)}
final-recipes-display.tsx:215 🍃 [Final Recipes] Initiating parallel generation for time slots: (3) ['morning', 'mid-day', 'night']
final-recipes-display.tsx:229 🔍 [Final Recipes] Created propertyOilSuggestions for streaming: {propertiesCount: 5, totalOilsCount: 40, propertiesSample: Array(2)}
final-recipes-display.tsx:239 ✅ [Final Recipes] FINAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:240 ✅ [Final Recipes] - healthConcern: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono'}
final-recipes-display.tsx:241 ✅ [Final Recipes] - demographics: {gender: 'female', ageCategory: 'teen', specificAge: 16}
final-recipes-display.tsx:242 ✅ [Final Recipes] - selectedCauses count: 3
final-recipes-display.tsx:243 ✅ [Final Recipes] - selectedSymptoms count: 2
final-recipes-display.tsx:244 ✅ [Final Recipes] - propertyOilSuggestions (from therapeuticProperties): 5
final-recipes-display.tsx:245 ✅ [Final Recipes] - timeSlots with time-period integration: (3) ['morning', 'mid-day', 'night']
use-create-recipe-streaming.ts:139 🍃 [Final Recipes] Starting recipe generation for time slots: (3) ['morning', 'mid-day', 'night']
use-create-recipe-streaming.ts:140 🔍 [Final Recipes] Input validation: {timeSlots: 3, hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 3, selectedSymptomsCount: 2, …}
use-create-recipe-streaming.ts:151 🔍 [Final Recipes] Detailed input data: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono', demographics: {…}, selectedCauses: Array(3), selectedSymptoms: Array(2), suggestedOilsByProperty: Array(5)}
use-create-recipe-streaming.ts:175 🔧 [Final Recipes] Creating request for morning time slot
use-create-recipe-streaming.ts:189 🔍 [Final Recipes] Request data for morning: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'morning', dataKeys: Array(9), hasAdditionalData: false}
use-create-recipe-streaming.ts:175 🔧 [Final Recipes] Creating request for mid-day time slot
use-create-recipe-streaming.ts:189 🔍 [Final Recipes] Request data for mid-day: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'mid-day', dataKeys: Array(9), hasAdditionalData: false}
use-create-recipe-streaming.ts:175 🔧 [Final Recipes] Creating request for night time slot
use-create-recipe-streaming.ts:189 🔍 [Final Recipes] Request data for night: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'night', dataKeys: Array(9), hasAdditionalData: false}
use-create-recipe-streaming.ts:231 🍃 [Final Recipes] Initiating 3 parallel streaming requests
use-parallel-streaming-engine.ts:63 [ParallelEngine] Starting stream for ID: morning (morning recipe)
use-parallel-streaming-engine.ts:64 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
use-parallel-streaming-engine.ts:63 [ParallelEngine] Starting stream for ID: mid-day (mid-day recipe)
use-parallel-streaming-engine.ts:64 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
use-parallel-streaming-engine.ts:63 [ParallelEngine] Starting stream for ID: night (night recipe)
use-parallel-streaming-engine.ts:64 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:47 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 3, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:65 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:66 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:85 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:86 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:87 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:88 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:93 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:94 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:95 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:47 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 3, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:65 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:66 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:85 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:86 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:87 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:88 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:93 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:94 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:95 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:390 🔍 [Final Recipes] useEffect triggered with data check
final-recipes-display.tsx:401 🔍 [Final Recipes] Data validation result (UPDATED):
final-recipes-display.tsx:402 🔍 [Final Recipes] - hasHealthConcern: true
final-recipes-display.tsx:403 🔍 [Final Recipes] - hasDemographics: true
final-recipes-display.tsx:404 🔍 [Final Recipes] - selectedCausesCount: 3
final-recipes-display.tsx:405 🔍 [Final Recipes] - selectedSymptomsCount: 2
final-recipes-display.tsx:406 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils (NEW): true
final-recipes-display.tsx:407 🔍 [Final Recipes] - suggestedOils.length (OLD/PROBLEMATIC): 0
final-recipes-display.tsx:408 🔍 [Final Recipes] - hasStartedGeneration: false
final-recipes-display.tsx:409 🔍 [Final Recipes] - hasRequiredData (FINAL): true
final-recipes-display.tsx:412 ✅ [Final Recipes] Auto-starting recipe generation with required data (using therapeuticProperties)
final-recipes-display.tsx:107 🍃 [Final Recipes] handleGenerateRecipes called
final-recipes-display.tsx:147 🍃 [Final Recipes] Starting recipe generation process
final-recipes-display.tsx:148 🔍 [Final Recipes] Input data summary: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono', demographics: {…}, selectedCausesCount: 3, selectedSymptomsCount: 2, suggestedOilsCount: 0, …}
final-recipes-display.tsx:162 🔍 [Final Recipes] Selected causes: (3) [{…}, {…}, {…}]
final-recipes-display.tsx:166 🔍 [Final Recipes] Selected symptoms: (2) [{…}, {…}]
final-recipes-display.tsx:170 🔍 [Final Recipes] Therapeutic properties with oils (UPDATED - same as debug overlay): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:186 🔍 [Final Recipes] Creating minimal data structure (same as debug overlay)...
final-recipes-display.tsx:203 🔍 [Final Recipes] Extracted oils from therapeuticProperties: {totalOilsCount: 18, oilsSample: Array(3)}
final-recipes-display.tsx:215 🍃 [Final Recipes] Initiating parallel generation for time slots: (3) ['morning', 'mid-day', 'night']
final-recipes-display.tsx:229 🔍 [Final Recipes] Created propertyOilSuggestions for streaming: {propertiesCount: 5, totalOilsCount: 40, propertiesSample: Array(2)}
final-recipes-display.tsx:239 ✅ [Final Recipes] FINAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:240 ✅ [Final Recipes] - healthConcern: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono'}
final-recipes-display.tsx:241 ✅ [Final Recipes] - demographics: {gender: 'female', ageCategory: 'teen', specificAge: 16}
final-recipes-display.tsx:242 ✅ [Final Recipes] - selectedCauses count: 3
final-recipes-display.tsx:243 ✅ [Final Recipes] - selectedSymptoms count: 2
final-recipes-display.tsx:244 ✅ [Final Recipes] - propertyOilSuggestions (from therapeuticProperties): 5
final-recipes-display.tsx:245 ✅ [Final Recipes] - timeSlots with time-period integration: (3) ['morning', 'mid-day', 'night']
use-create-recipe-streaming.ts:139 🍃 [Final Recipes] Starting recipe generation for time slots: (3) ['morning', 'mid-day', 'night']
use-create-recipe-streaming.ts:140 🔍 [Final Recipes] Input validation: {timeSlots: 3, hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 3, selectedSymptomsCount: 2, …}
use-create-recipe-streaming.ts:151 🔍 [Final Recipes] Detailed input data: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono', demographics: {…}, selectedCauses: Array(3), selectedSymptoms: Array(2), suggestedOilsByProperty: Array(5)}
use-create-recipe-streaming.ts:175 🔧 [Final Recipes] Creating request for morning time slot
use-create-recipe-streaming.ts:189 🔍 [Final Recipes] Request data for morning: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'morning', dataKeys: Array(9), hasAdditionalData: false}
use-create-recipe-streaming.ts:175 🔧 [Final Recipes] Creating request for mid-day time slot
use-create-recipe-streaming.ts:189 🔍 [Final Recipes] Request data for mid-day: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'mid-day', dataKeys: Array(9), hasAdditionalData: false}
use-create-recipe-streaming.ts:175 🔧 [Final Recipes] Creating request for night time slot
use-create-recipe-streaming.ts:189 🔍 [Final Recipes] Request data for night: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'night', dataKeys: Array(9), hasAdditionalData: false}
use-create-recipe-streaming.ts:231 🍃 [Final Recipes] Initiating 3 parallel streaming requests
use-parallel-streaming-engine.ts:63 [ParallelEngine] Starting stream for ID: morning (morning recipe)
use-parallel-streaming-engine.ts:64 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
 [ParallelEngine] Starting stream for ID: mid-day (mid-day recipe)
 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
 [ParallelEngine] Starting stream for ID: night (night recipe)
 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
 🍃 [Final Recipes] Component mounting/rendering
 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 3, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
 🔍 [Final Recipes] Data availability analysis:
 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
 🔍 [Final Recipes] - Total oils across all properties: 40
 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
 🍃 [Final Recipes] Component mounting/rendering
 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 3, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
 🔍 [Final Recipes] Data availability analysis:
 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
 🔍 [Final Recipes] - Total oils across all properties: 40
 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
 🔍 [Final Recipes] useEffect triggered with data check
 🔍 [Final Recipes] Data validation result (UPDATED):
 🔍 [Final Recipes] - hasHealthConcern: true
 🔍 [Final Recipes] - hasDemographics: true
 🔍 [Final Recipes] - selectedCausesCount: 3
 🔍 [Final Recipes] - selectedSymptomsCount: 2
 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils (NEW): true
 🔍 [Final Recipes] - suggestedOils.length (OLD/PROBLEMATIC): 0
 🔍 [Final Recipes] - hasStartedGeneration: false
 🔍 [Final Recipes] - hasRequiredData (FINAL): true
 ✅ [Final Recipes] Auto-starting recipe generation with required data (using therapeuticProperties)
 🍃 [Final Recipes] handleGenerateRecipes called
 🍃 [Final Recipes] Starting recipe generation process
 🔍 [Final Recipes] Input data summary: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono', demographics: {…}, selectedCausesCount: 3, selectedSymptomsCount: 2, suggestedOilsCount: 0, …}
 🔍 [Final Recipes] Selected causes: (3) [{…}, {…}, {…}]
 🔍 [Final Recipes] Selected symptoms: (2) [{…}, {…}]
 🔍 [Final Recipes] Therapeutic properties with oils (UPDATED - same as debug overlay): (5) [{…}, {…}, {…}, {…}, {…}]
 🔍 [Final Recipes] Creating minimal data structure (same as debug overlay)...
 🔍 [Final Recipes] Extracted oils from therapeuticProperties: {totalOilsCount: 18, oilsSample: Array(3)}
 🍃 [Final Recipes] Initiating parallel generation for time slots: (3) ['morning', 'mid-day', 'night']
 🔍 [Final Recipes] Created propertyOilSuggestions for streaming: {propertiesCount: 5, totalOilsCount: 40, propertiesSample: Array(2)}
 ✅ [Final Recipes] FINAL DATA STRUCTURE (same as debug overlay):
 ✅ [Final Recipes] - healthConcern: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono'}
 ✅ [Final Recipes] - demographics: {gender: 'female', ageCategory: 'teen', specificAge: 16}
 ✅ [Final Recipes] - selectedCauses count: 3
 ✅ [Final Recipes] - selectedSymptoms count: 2
 ✅ [Final Recipes] - propertyOilSuggestions (from therapeuticProperties): 5
 ✅ [Final Recipes] - timeSlots with time-period integration: (3) ['morning', 'mid-day', 'night']
 🍃 [Final Recipes] Starting recipe generation for time slots: (3) ['morning', 'mid-day', 'night']
 🔍 [Final Recipes] Input validation: {timeSlots: 3, hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 3, selectedSymptomsCount: 2, …}
 🔍 [Final Recipes] Detailed input data: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono', demographics: {…}, selectedCauses: Array(3), selectedSymptoms: Array(2), suggestedOilsByProperty: Array(5)}
 🔧 [Final Recipes] Creating request for morning time slot
 🔍 [Final Recipes] Request data for morning: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'morning', dataKeys: Array(9), hasAdditionalData: false}
 🔧 [Final Recipes] Creating request for mid-day time slot
 🔍 [Final Recipes] Request data for mid-day: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'mid-day', dataKeys: Array(9), hasAdditionalData: false}
 🔧 [Final Recipes] Creating request for night time slot
 🔍 [Final Recipes] Request data for night: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'night', dataKeys: Array(9), hasAdditionalData: false}
 🍃 [Final Recipes] Initiating 3 parallel streaming requests
 [ParallelEngine] Starting stream for ID: morning (morning recipe)
 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
 [ParallelEngine] Starting stream for ID: mid-day (mid-day recipe)
 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
 [ParallelEngine] Starting stream for ID: night (night recipe)
 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
 🍃 [Final Recipes] Component mounting/rendering
 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 3, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
 🔍 [Final Recipes] Data availability analysis:
 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
 🔍 [Final Recipes] - Total oils across all properties: 40
 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
 🍃 [Final Recipes] Component mounting/rendering
 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 3, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
 🔍 [Final Recipes] Data availability analysis:
 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
 🔍 [Final Recipes] - Total oils across all properties: 40
 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
 🔍 [Final Recipes] useEffect triggered with data check
 🔍 [Final Recipes] Data validation result (UPDATED):
 🔍 [Final Recipes] - hasHealthConcern: true
 🔍 [Final Recipes] - hasDemographics: true
 🔍 [Final Recipes] - selectedCausesCount: 3
 🔍 [Final Recipes] - selectedSymptomsCount: 2
 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils (NEW): true
 🔍 [Final Recipes] - suggestedOils.length (OLD/PROBLEMATIC): 0
 🔍 [Final Recipes] - hasStartedGeneration: false
 🔍 [Final Recipes] - hasRequiredData (FINAL): true
 ✅ [Final Recipes] Auto-starting recipe generation with required data (using therapeuticProperties)
 🍃 [Final Recipes] handleGenerateRecipes called
 🍃 [Final Recipes] Starting recipe generation process
 🔍 [Final Recipes] Input data summary: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono', demographics: {…}, selectedCausesCount: 3, selectedSymptomsCount: 2, suggestedOilsCount: 0, …}
 🔍 [Final Recipes] Selected causes: (3) [{…}, {…}, {…}]
 🔍 [Final Recipes] Selected symptoms: (2) [{…}, {…}]
 🔍 [Final Recipes] Therapeutic properties with oils (UPDATED - same as debug overlay): (5) [{…}, {…}, {…}, {…}, {…}]
 🔍 [Final Recipes] Creating minimal data structure (same as debug overlay)...
 🔍 [Final Recipes] Extracted oils from therapeuticProperties: {totalOilsCount: 18, oilsSample: Array(3)}
 🍃 [Final Recipes] Initiating parallel generation for time slots: (3) ['morning', 'mid-day', 'night']
 🔍 [Final Recipes] Created propertyOilSuggestions for streaming: {propertiesCount: 5, totalOilsCount: 40, propertiesSample: Array(2)}
 ✅ [Final Recipes] FINAL DATA STRUCTURE (same as debug overlay):
 ✅ [Final Recipes] - healthConcern: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono'}
 ✅ [Final Recipes] - demographics: {gender: 'female', ageCategory: 'teen', specificAge: 16}
 ✅ [Final Recipes] - selectedCauses count: 3
 ✅ [Final Recipes] - selectedSymptoms count: 2
 ✅ [Final Recipes] - propertyOilSuggestions (from therapeuticProperties): 5
 ✅ [Final Recipes] - timeSlots with time-period integration: (3) ['morning', 'mid-day', 'night']
 🍃 [Final Recipes] Starting recipe generation for time slots: (3) ['morning', 'mid-day', 'night']
 🔍 [Final Recipes] Input validation: {timeSlots: 3, hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 3, selectedSymptomsCount: 2, …}
 🔍 [Final Recipes] Detailed input data: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono', demographics: {…}, selectedCauses: Array(3), selectedSymptoms: Array(2), suggestedOilsByProperty: Array(5)}
 🔧 [Final Recipes] Creating request for morning time slot
 🔍 [Final Recipes] Request data for morning: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'morning', dataKeys: Array(9), hasAdditionalData: false}
 🔧 [Final Recipes] Creating request for mid-day time slot
 🔍 [Final Recipes] Request data for mid-day: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'mid-day', dataKeys: Array(9), hasAdditionalData: false}
 🔧 [Final Recipes] Creating request for night time slot
 🔍 [Final Recipes] Request data for night: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'night', dataKeys: Array(9), hasAdditionalData: false}
 🍃 [Final Recipes] Initiating 3 parallel streaming requests
 [ParallelEngine] Starting stream for ID: morning (morning recipe)
 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
 [ParallelEngine] Starting stream for ID: mid-day (mid-day recipe)
 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
 [ParallelEngine] Starting stream for ID: night (night recipe)
 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
 🍃 [Final Recipes] Component mounting/rendering
 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 3, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
 🔍 [Final Recipes] Data availability analysis:
 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
 🔍 [Final Recipes] - Total oils across all properties: 40
 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
 🍃 [Final Recipes] Component mounting/rendering
 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 3, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
 🔍 [Final Recipes] Data availability analysis:
 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
 🔍 [Final Recipes] - Total oils across all properties: 40
 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
 🔍 [Final Recipes] useEffect triggered with data check
 🔍 [Final Recipes] Data validation result (UPDATED):
 🔍 [Final Recipes] - hasHealthConcern: true
 🔍 [Final Recipes] - hasDemographics: true
 🔍 [Final Recipes] - selectedCausesCount: 3
 🔍 [Final Recipes] - selectedSymptomsCount: 2
 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils (NEW): true
 🔍 [Final Recipes] - suggestedOils.length (OLD/PROBLEMATIC): 0
 🔍 [Final Recipes] - hasStartedGeneration: false
 🔍 [Final Recipes] - hasRequiredData (FINAL): true
 ✅ [Final Recipes] Auto-starting recipe generation with required data (using therapeuticProperties)
final-recipes-display.tsx:107 🍃 [Final Recipes] handleGenerateRecipes called
final-recipes-display.tsx:147 🍃 [Final Recipes] Starting recipe generation process
final-recipes-display.tsx:148 🔍 [Final Recipes] Input data summary: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono', demographics: {…}, selectedCausesCount: 3, selectedSymptomsCount: 2, suggestedOilsCount: 0, …}
final-recipes-display.tsx:162 🔍 [Final Recipes] Selected causes: (3) [{…}, {…}, {…}]
final-recipes-display.tsx:166 🔍 [Final Recipes] Selected symptoms: (2) [{…}, {…}]
final-recipes-display.tsx:170 🔍 [Final Recipes] Therapeutic properties with oils (UPDATED - same as debug overlay): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:186 🔍 [Final Recipes] Creating minimal data structure (same as debug overlay)...
final-recipes-display.tsx:203 🔍 [Final Recipes] Extracted oils from therapeuticProperties: {totalOilsCount: 18, oilsSample: Array(3)}
final-recipes-display.tsx:215 🍃 [Final Recipes] Initiating parallel generation for time slots: (3) ['morning', 'mid-day', 'night']
final-recipes-display.tsx:229 🔍 [Final Recipes] Created propertyOilSuggestions for streaming: {propertiesCount: 5, totalOilsCount: 40, propertiesSample: Array(2)}
final-recipes-display.tsx:239 ✅ [Final Recipes] FINAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:240 ✅ [Final Recipes] - healthConcern: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono'}
final-recipes-display.tsx:241 ✅ [Final Recipes] - demographics: {gender: 'female', ageCategory: 'teen', specificAge: 16}
final-recipes-display.tsx:242 ✅ [Final Recipes] - selectedCauses count: 3
final-recipes-display.tsx:243 ✅ [Final Recipes] - selectedSymptoms count: 2
final-recipes-display.tsx:244 ✅ [Final Recipes] - propertyOilSuggestions (from therapeuticProperties): 5
final-recipes-display.tsx:245 ✅ [Final Recipes] - timeSlots with time-period integration: (3) ['morning', 'mid-day', 'night']
use-create-recipe-streaming.ts:139 🍃 [Final Recipes] Starting recipe generation for time slots: (3) ['morning', 'mid-day', 'night']
use-create-recipe-streaming.ts:140 🔍 [Final Recipes] Input validation: {timeSlots: 3, hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 3, selectedSymptomsCount: 2, …}
use-create-recipe-streaming.ts:151 🔍 [Final Recipes] Detailed input data: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono', demographics: {…}, selectedCauses: Array(3), selectedSymptoms: Array(2), suggestedOilsByProperty: Array(5)}
use-create-recipe-streaming.ts:175 🔧 [Final Recipes] Creating request for morning time slot
use-create-recipe-streaming.ts:189 🔍 [Final Recipes] Request data for morning: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'morning', dataKeys: Array(9), hasAdditionalData: false}
use-create-recipe-streaming.ts:175 🔧 [Final Recipes] Creating request for mid-day time slot
use-create-recipe-streaming.ts:189 🔍 [Final Recipes] Request data for mid-day: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'mid-day', dataKeys: Array(9), hasAdditionalData: false}
use-create-recipe-streaming.ts:175 🔧 [Final Recipes] Creating request for night time slot
use-create-recipe-streaming.ts:189 🔍 [Final Recipes] Request data for night: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'night', dataKeys: Array(9), hasAdditionalData: false}
use-create-recipe-streaming.ts:231 🍃 [Final Recipes] Initiating 3 parallel streaming requests
use-parallel-streaming-engine.ts:63 [ParallelEngine] Starting stream for ID: morning (morning recipe)
use-parallel-streaming-engine.ts:64 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
use-parallel-streaming-engine.ts:63 [ParallelEngine] Starting stream for ID: mid-day (mid-day recipe)
use-parallel-streaming-engine.ts:64 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
use-parallel-streaming-engine.ts:63 [ParallelEngine] Starting stream for ID: night (night recipe)
use-parallel-streaming-engine.ts:64 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:47 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 3, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:65 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:66 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:85 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:86 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:87 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:88 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:93 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:94 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:95 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:47 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 3, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:65 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:66 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:85 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:86 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:87 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:88 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:93 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:94 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:95 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:390 🔍 [Final Recipes] useEffect triggered with data check
final-recipes-display.tsx:401 🔍 [Final Recipes] Data validation result (UPDATED):
final-recipes-display.tsx:402 🔍 [Final Recipes] - hasHealthConcern: true
final-recipes-display.tsx:403 🔍 [Final Recipes] - hasDemographics: true
final-recipes-display.tsx:404 🔍 [Final Recipes] - selectedCausesCount: 3
final-recipes-display.tsx:405 🔍 [Final Recipes] - selectedSymptomsCount: 2
final-recipes-display.tsx:406 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils (NEW): true
final-recipes-display.tsx:407 🔍 [Final Recipes] - suggestedOils.length (OLD/PROBLEMATIC): 0
final-recipes-display.tsx:408 🔍 [Final Recipes] - hasStartedGeneration: false
final-recipes-display.tsx:409 🔍 [Final Recipes] - hasRequiredData (FINAL): true
final-recipes-display.tsx:412 ✅ [Final Recipes] Auto-starting recipe generation with required data (using therapeuticProperties)
final-recipes-display.tsx:107 🍃 [Final Recipes] handleGenerateRecipes called
final-recipes-display.tsx:147 🍃 [Final Recipes] Starting recipe generation process
final-recipes-display.tsx:148 🔍 [Final Recipes] Input data summary: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono', demographics: {…}, selectedCausesCount: 3, selectedSymptomsCount: 2, suggestedOilsCount: 0, …}
final-recipes-display.tsx:162 🔍 [Final Recipes] Selected causes: (3) [{…}, {…}, {…}]
final-recipes-display.tsx:166 🔍 [Final Recipes] Selected symptoms: (2) [{…}, {…}]
final-recipes-display.tsx:170 🔍 [Final Recipes] Therapeutic properties with oils (UPDATED - same as debug overlay): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:186 🔍 [Final Recipes] Creating minimal data structure (same as debug overlay)...
final-recipes-display.tsx:203 🔍 [Final Recipes] Extracted oils from therapeuticProperties: {totalOilsCount: 18, oilsSample: Array(3)}
final-recipes-display.tsx:215 🍃 [Final Recipes] Initiating parallel generation for time slots: (3) ['morning', 'mid-day', 'night']
final-recipes-display.tsx:229 🔍 [Final Recipes] Created propertyOilSuggestions for streaming: {propertiesCount: 5, totalOilsCount: 40, propertiesSample: Array(2)}
final-recipes-display.tsx:239 ✅ [Final Recipes] FINAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:240 ✅ [Final Recipes] - healthConcern: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono'}
final-recipes-display.tsx:241 ✅ [Final Recipes] - demographics: {gender: 'female', ageCategory: 'teen', specificAge: 16}
final-recipes-display.tsx:242 ✅ [Final Recipes] - selectedCauses count: 3
final-recipes-display.tsx:243 ✅ [Final Recipes] - selectedSymptoms count: 2
final-recipes-display.tsx:244 ✅ [Final Recipes] - propertyOilSuggestions (from therapeuticProperties): 5
final-recipes-display.tsx:245 ✅ [Final Recipes] - timeSlots with time-period integration: (3) ['morning', 'mid-day', 'night']
use-create-recipe-streaming.ts:139 🍃 [Final Recipes] Starting recipe generation for time slots: (3) ['morning', 'mid-day', 'night']
use-create-recipe-streaming.ts:140 🔍 [Final Recipes] Input validation: {timeSlots: 3, hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 3, selectedSymptomsCount: 2, …}
use-create-recipe-streaming.ts:151 🔍 [Final Recipes] Detailed input data: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono', demographics: {…}, selectedCauses: Array(3), selectedSymptoms: Array(2), suggestedOilsByProperty: Array(5)}
use-create-recipe-streaming.ts:175 🔧 [Final Recipes] Creating request for morning time slot
use-create-recipe-streaming.ts:189 🔍 [Final Recipes] Request data for morning: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'morning', dataKeys: Array(9), hasAdditionalData: false}
use-create-recipe-streaming.ts:175 🔧 [Final Recipes] Creating request for mid-day time slot
use-create-recipe-streaming.ts:189 🔍 [Final Recipes] Request data for mid-day: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'mid-day', dataKeys: Array(9), hasAdditionalData: false}
use-create-recipe-streaming.ts:175 🔧 [Final Recipes] Creating request for night time slot
use-create-recipe-streaming.ts:189 🔍 [Final Recipes] Request data for night: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'night', dataKeys: Array(9), hasAdditionalData: false}
use-create-recipe-streaming.ts:231 🍃 [Final Recipes] Initiating 3 parallel streaming requests
use-parallel-streaming-engine.ts:63 [ParallelEngine] Starting stream for ID: morning (morning recipe)
use-parallel-streaming-engine.ts:64 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
use-parallel-streaming-engine.ts:63 [ParallelEngine] Starting stream for ID: mid-day (mid-day recipe)
use-parallel-streaming-engine.ts:64 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
use-parallel-streaming-engine.ts:63 [ParallelEngine] Starting stream for ID: night (night recipe)
use-parallel-streaming-engine.ts:64 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
 🍃 [Final Recipes] Component mounting/rendering
 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 3, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
 🔍 [Final Recipes] Data availability analysis:
 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
 🔍 [Final Recipes] - Total oils across all properties: 40
 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
 🍃 [Final Recipes] Component mounting/rendering
 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 3, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
 🔍 [Final Recipes] Data availability analysis:
 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
 🔍 [Final Recipes] - Total oils across all properties: 40
 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
 🔍 [Final Recipes] useEffect triggered with data check
 🔍 [Final Recipes] Data validation result (UPDATED):
 🔍 [Final Recipes] - hasHealthConcern: true
 🔍 [Final Recipes] - hasDemographics: true
 🔍 [Final Recipes] - selectedCausesCount: 3
 🔍 [Final Recipes] - selectedSymptomsCount: 2
 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils (NEW): true
 🔍 [Final Recipes] - suggestedOils.length (OLD/PROBLEMATIC): 0
 🔍 [Final Recipes] - hasStartedGeneration: false
 🔍 [Final Recipes] - hasRequiredData (FINAL): true
 ✅ [Final Recipes] Auto-starting recipe generation with required data (using therapeuticProperties)
 🍃 [Final Recipes] handleGenerateRecipes called
 🍃 [Final Recipes] Starting recipe generation process
 🔍 [Final Recipes] Input data summary: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono', demographics: {…}, selectedCausesCount: 3, selectedSymptomsCount: 2, suggestedOilsCount: 0, …}
 🔍 [Final Recipes] Selected causes: (3) [{…}, {…}, {…}]
 🔍 [Final Recipes] Selected symptoms: (2) [{…}, {…}]
 🔍 [Final Recipes] Therapeutic properties with oils (UPDATED - same as debug overlay): (5) [{…}, {…}, {…}, {…}, {…}]
 🔍 [Final Recipes] Creating minimal data structure (same as debug overlay)...
 🔍 [Final Recipes] Extracted oils from therapeuticProperties: {totalOilsCount: 18, oilsSample: Array(3)}
 🍃 [Final Recipes] Initiating parallel generation for time slots: (3) ['morning', 'mid-day', 'night']
 🔍 [Final Recipes] Created propertyOilSuggestions for streaming: {propertiesCount: 5, totalOilsCount: 40, propertiesSample: Array(2)}
final-recipes-display.tsx:239 ✅ [Final Recipes] FINAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:240 ✅ [Final Recipes] - healthConcern: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono'}
final-recipes-display.tsx:241 ✅ [Final Recipes] - demographics: {gender: 'female', ageCategory: 'teen', specificAge: 16}
final-recipes-display.tsx:242 ✅ [Final Recipes] - selectedCauses count: 3
final-recipes-display.tsx:243 ✅ [Final Recipes] - selectedSymptoms count: 2
final-recipes-display.tsx:244 ✅ [Final Recipes] - propertyOilSuggestions (from therapeuticProperties): 5
final-recipes-display.tsx:245 ✅ [Final Recipes] - timeSlots with time-period integration: (3) ['morning', 'mid-day', 'night']
use-create-recipe-streaming.ts:139 🍃 [Final Recipes] Starting recipe generation for time slots: (3) ['morning', 'mid-day', 'night']
use-create-recipe-streaming.ts:140 🔍 [Final Recipes] Input validation: {timeSlots: 3, hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 3, selectedSymptomsCount: 2, …}
use-create-recipe-streaming.ts:151 🔍 [Final Recipes] Detailed input data: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono', demographics: {…}, selectedCauses: Array(3), selectedSymptoms: Array(2), suggestedOilsByProperty: Array(5)}
use-create-recipe-streaming.ts:175 🔧 [Final Recipes] Creating request for morning time slot
use-create-recipe-streaming.ts:189 🔍 [Final Recipes] Request data for morning: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'morning', dataKeys: Array(9), hasAdditionalData: false}
use-create-recipe-streaming.ts:175 🔧 [Final Recipes] Creating request for mid-day time slot
use-create-recipe-streaming.ts:189 🔍 [Final Recipes] Request data for mid-day: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'mid-day', dataKeys: Array(9), hasAdditionalData: false}
use-create-recipe-streaming.ts:175 🔧 [Final Recipes] Creating request for night time slot
use-create-recipe-streaming.ts:189 🔍 [Final Recipes] Request data for night: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'night', dataKeys: Array(9), hasAdditionalData: false}
use-create-recipe-streaming.ts:231 🍃 [Final Recipes] Initiating 3 parallel streaming requests
use-parallel-streaming-engine.ts:63 [ParallelEngine] Starting stream for ID: morning (morning recipe)
use-parallel-streaming-engine.ts:64 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
use-parallel-streaming-engine.ts:63 [ParallelEngine] Starting stream for ID: mid-day (mid-day recipe)
use-parallel-streaming-engine.ts:64 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
use-parallel-streaming-engine.ts:63 [ParallelEngine] Starting stream for ID: night (night recipe)
use-parallel-streaming-engine.ts:64 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:47 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 3, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:65 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:66 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:85 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:86 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:87 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:88 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:93 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:94 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:95 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:47 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 3, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:65 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:66 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:85 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:86 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:87 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:88 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:93 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:94 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:95 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:390 🔍 [Final Recipes] useEffect triggered with data check
final-recipes-display.tsx:401 🔍 [Final Recipes] Data validation result (UPDATED):
final-recipes-display.tsx:402 🔍 [Final Recipes] - hasHealthConcern: true
final-recipes-display.tsx:403 🔍 [Final Recipes] - hasDemographics: true
final-recipes-display.tsx:404 🔍 [Final Recipes] - selectedCausesCount: 3
final-recipes-display.tsx:405 🔍 [Final Recipes] - selectedSymptomsCount: 2
final-recipes-display.tsx:406 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils (NEW): true
final-recipes-display.tsx:407 🔍 [Final Recipes] - suggestedOils.length (OLD/PROBLEMATIC): 0
final-recipes-display.tsx:408 🔍 [Final Recipes] - hasStartedGeneration: false
final-recipes-display.tsx:409 🔍 [Final Recipes] - hasRequiredData (FINAL): true
final-recipes-display.tsx:412 ✅ [Final Recipes] Auto-starting recipe generation with required data (using therapeuticProperties)
final-recipes-display.tsx:107 🍃 [Final Recipes] handleGenerateRecipes called
