properties-display.tsx:427 🚀 [Properties Navigation] Continue button clicked
use-recipe-navigation.ts:150 🔍 [Navigation Hook] canGoNext called for current step: properties
use-recipe-navigation.ts:157 🔍 [Navigation Hook] Checking navigation to next step: final-recipes
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: final-recipes
recipe-store.ts:203 🔍 [Store Navigation] Final Recipes validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, therapeuticPropertiesCount: 7, …}
recipe-store.ts:218 🔍 [Store Navigation] Property 1: <PERSON><PERSON><PERSON> da dor {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 2: Relaxamento muscular {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 3: Ação anti-inflamatória {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 4: Estabilização emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 5: Melhora da postura {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 6: Tonicidade {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 7: Aperfeiçoamento emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
use-recipe-navigation.ts:159 🔍 [Navigation Hook] canGoNext result: true
properties-display.tsx:431 🔍 [Properties Navigation] Navigation validation: {canGoNext: true, therapeuticPropertiesCount: 7, enrichedPropertiesCount: 7, allPropertiesEnriched: true, hasHealthConcern: true, …}
properties-display.tsx:444 🔍 [Properties Navigation] Property 1: Alívio da dor {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
properties-display.tsx:444 🔍 [Properties Navigation] Property 2: Relaxamento muscular {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
properties-display.tsx:444 🔍 [Properties Navigation] Property 3: Ação anti-inflamatória {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
properties-display.tsx:444 🔍 [Properties Navigation] Property 4: Estabilização emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
properties-display.tsx:444 🔍 [Properties Navigation] Property 5: Melhora da postura {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
properties-display.tsx:444 🔍 [Properties Navigation] Property 6: Tonicidade {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
properties-display.tsx:444 🔍 [Properties Navigation] Property 7: Aperfeiçoamento emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
properties-display.tsx:455 ✅ [Properties Navigation] Validation passed, navigating to next step
use-recipe-navigation.ts:243 🎯 [2025-07-15T00:04:38.512Z] goToNext called: {currentStep: 'properties', nextStep: 'final-recipes', hasNext: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: final-recipes
recipe-store.ts:203 🔍 [Store Navigation] Final Recipes validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, therapeuticPropertiesCount: 7, …}
recipe-store.ts:218 🔍 [Store Navigation] Property 1: Alívio da dor {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 2: Relaxamento muscular {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 3: Ação anti-inflamatória {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 4: Estabilização emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 5: Melhora da postura {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 6: Tonicidade {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 7: Aperfeiçoamento emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
use-recipe-navigation.ts:259 🔍 [2025-07-15T00:04:38.512Z] goToNext: Navigation validation result: true
use-recipe-navigation.ts:270 ✅ [2025-07-15T00:04:38.512Z] goToNext: Marking current step completed: properties
use-recipe-navigation.ts:273 🚀 [2025-07-15T00:04:38.512Z] goToNext: Calling goToStep with: final-recipes
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: final-recipes
recipe-store.ts:203 🔍 [Store Navigation] Final Recipes validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, therapeuticPropertiesCount: 7, …}
recipe-store.ts:218 🔍 [Store Navigation] Property 1: Alívio da dor {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 2: Relaxamento muscular {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 3: Ação anti-inflamatória {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 4: Estabilização emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 5: Melhora da postura {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 6: Tonicidade {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 7: Aperfeiçoamento emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
use-recipe-navigation.ts:207 🚀 [2025-07-15T00:04:38.517Z] Navigation: Setting current step in store: final-recipes
use-recipe-navigation.ts:212 🌐 [2025-07-15T00:04:38.517Z] Navigation: Pushing to URL: /dashboard/create-recipe/final-recipes
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: health-concern
recipe-store.ts:149 ✅ [Store Navigation] Health concern step - always allowed
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: demographics
recipe-store.ts:157 🔍 [Store Navigation] Demographics validation: {hasHealthConcern: true, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: causes
recipe-store.ts:165 🔍 [Store Navigation] Causes validation: {hasHealthConcern: true, hasDemographics: true, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: symptoms
recipe-store.ts:174 🔍 [Store Navigation] Symptoms validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: properties
recipe-store.ts:185 🔍 [Store Navigation] Properties validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: final-recipes
recipe-store.ts:203 🔍 [Store Navigation] Final Recipes validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, therapeuticPropertiesCount: 7, …}
recipe-store.ts:218 🔍 [Store Navigation] Property 1: Alívio da dor {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 2: Relaxamento muscular {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 3: Ação anti-inflamatória {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 4: Estabilização emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 5: Melhora da postura {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 6: Tonicidade {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 7: Aperfeiçoamento emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: health-concern
recipe-store.ts:149 ✅ [Store Navigation] Health concern step - always allowed
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: demographics
recipe-store.ts:157 🔍 [Store Navigation] Demographics validation: {hasHealthConcern: true, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: causes
recipe-store.ts:165 🔍 [Store Navigation] Causes validation: {hasHealthConcern: true, hasDemographics: true, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: symptoms
recipe-store.ts:174 🔍 [Store Navigation] Symptoms validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: properties
recipe-store.ts:185 🔍 [Store Navigation] Properties validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: final-recipes
recipe-store.ts:203 🔍 [Store Navigation] Final Recipes validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, therapeuticPropertiesCount: 7, …}
recipe-store.ts:218 🔍 [Store Navigation] Property 1: Alívio da dor {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 2: Relaxamento muscular {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 3: Ação anti-inflamatória {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 4: Estabilização emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 5: Melhora da postura {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 6: Tonicidade {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 7: Aperfeiçoamento emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
use-recipe-navigation.ts:150 🔍 [Navigation Hook] canGoNext called for current step: final-recipes
use-recipe-navigation.ts:153 ❌ [Navigation Hook] No next step available
use-recipe-navigation.ts:150 🔍 [Navigation Hook] canGoNext called for current step: final-recipes
use-recipe-navigation.ts:153 ❌ [Navigation Hook] No next step available
wizard-container.tsx:105 🔄 [2025-07-15T00:04:38.696Z] WizardContainer: Syncing URL step with store: {urlStep: 'properties', storeStep: 'final-recipes'}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: health-concern
recipe-store.ts:149 ✅ [Store Navigation] Health concern step - always allowed
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: demographics
recipe-store.ts:157 🔍 [Store Navigation] Demographics validation: {hasHealthConcern: true, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: causes
recipe-store.ts:165 🔍 [Store Navigation] Causes validation: {hasHealthConcern: true, hasDemographics: true, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: symptoms
recipe-store.ts:174 🔍 [Store Navigation] Symptoms validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: properties
recipe-store.ts:185 🔍 [Store Navigation] Properties validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: final-recipes
recipe-store.ts:203 🔍 [Store Navigation] Final Recipes validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, therapeuticPropertiesCount: 7, …}
recipe-store.ts:218 🔍 [Store Navigation] Property 1: Alívio da dor {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 2: Relaxamento muscular {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 3: Ação anti-inflamatória {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 4: Estabilização emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 5: Melhora da postura {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 6: Tonicidade {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 7: Aperfeiçoamento emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: health-concern
recipe-store.ts:149 ✅ [Store Navigation] Health concern step - always allowed
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: demographics
recipe-store.ts:157 🔍 [Store Navigation] Demographics validation: {hasHealthConcern: true, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: causes
recipe-store.ts:165 🔍 [Store Navigation] Causes validation: {hasHealthConcern: true, hasDemographics: true, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: symptoms
recipe-store.ts:174 🔍 [Store Navigation] Symptoms validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: properties
recipe-store.ts:185 🔍 [Store Navigation] Properties validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: final-recipes
recipe-store.ts:203 🔍 [Store Navigation] Final Recipes validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, therapeuticPropertiesCount: 7, …}
recipe-store.ts:218 🔍 [Store Navigation] Property 1: Alívio da dor {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 2: Relaxamento muscular {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 3: Ação anti-inflamatória {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 4: Estabilização emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 5: Melhora da postura {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 6: Tonicidade {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 7: Aperfeiçoamento emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
use-recipe-navigation.ts:150 🔍 [Navigation Hook] canGoNext called for current step: properties
use-recipe-navigation.ts:157 🔍 [Navigation Hook] Checking navigation to next step: final-recipes
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: final-recipes
recipe-store.ts:203 🔍 [Store Navigation] Final Recipes validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, therapeuticPropertiesCount: 7, …}
recipe-store.ts:218 🔍 [Store Navigation] Property 1: Alívio da dor {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 2: Relaxamento muscular {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 3: Ação anti-inflamatória {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 4: Estabilização emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 5: Melhora da postura {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 6: Tonicidade {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 7: Aperfeiçoamento emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
use-recipe-navigation.ts:159 🔍 [Navigation Hook] canGoNext result: true
use-recipe-navigation.ts:150 🔍 [Navigation Hook] canGoNext called for current step: properties
use-recipe-navigation.ts:157 🔍 [Navigation Hook] Checking navigation to next step: final-recipes
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: final-recipes
recipe-store.ts:203 🔍 [Store Navigation] Final Recipes validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, therapeuticPropertiesCount: 7, …}
recipe-store.ts:218 🔍 [Store Navigation] Property 1: Alívio da dor {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 2: Relaxamento muscular {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 3: Ação anti-inflamatória {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 4: Estabilização emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 5: Melhora da postura {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 6: Tonicidade {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 7: Aperfeiçoamento emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
use-recipe-navigation.ts:159 🔍 [Navigation Hook] canGoNext result: true
use-auth.ts:236 [useAuth] State changed: authenticated, ready, ok {performance: {…}, retryCount: 0, hasProfile: true}
use-auth.ts:236 [useAuth] State changed: authenticated, ready, ok {performance: {…}, retryCount: 0, hasProfile: true}
use-auth.ts:236 [useAuth] State changed: authenticated, ready, ok {performance: {…}, retryCount: 0, hasProfile: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: health-concern
recipe-store.ts:149 ✅ [Store Navigation] Health concern step - always allowed
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: demographics
recipe-store.ts:157 🔍 [Store Navigation] Demographics validation: {hasHealthConcern: true, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: causes
recipe-store.ts:165 🔍 [Store Navigation] Causes validation: {hasHealthConcern: true, hasDemographics: true, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: symptoms
recipe-store.ts:174 🔍 [Store Navigation] Symptoms validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: properties
recipe-store.ts:185 🔍 [Store Navigation] Properties validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: final-recipes
recipe-store.ts:203 🔍 [Store Navigation] Final Recipes validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, therapeuticPropertiesCount: 7, …}
recipe-store.ts:218 🔍 [Store Navigation] Property 1: Alívio da dor {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 2: Relaxamento muscular {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 3: Ação anti-inflamatória {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 4: Estabilização emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 5: Melhora da postura {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 6: Tonicidade {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 7: Aperfeiçoamento emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: health-concern
recipe-store.ts:149 ✅ [Store Navigation] Health concern step - always allowed
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: demographics
recipe-store.ts:157 🔍 [Store Navigation] Demographics validation: {hasHealthConcern: true, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: causes
recipe-store.ts:165 🔍 [Store Navigation] Causes validation: {hasHealthConcern: true, hasDemographics: true, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: symptoms
recipe-store.ts:174 🔍 [Store Navigation] Symptoms validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: properties
recipe-store.ts:185 🔍 [Store Navigation] Properties validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: final-recipes
recipe-store.ts:203 🔍 [Store Navigation] Final Recipes validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, therapeuticPropertiesCount: 7, …}
recipe-store.ts:218 🔍 [Store Navigation] Property 1: Alívio da dor {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 2: Relaxamento muscular {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 3: Ação anti-inflamatória {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 4: Estabilização emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 5: Melhora da postura {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 6: Tonicidade {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 7: Aperfeiçoamento emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
use-auth.ts:236 [useAuth] State changed: authenticated, ready, ok {performance: {…}, retryCount: 0, hasProfile: true}
use-auth.ts:236 [useAuth] State changed: authenticated, ready, ok {performance: {…}, retryCount: 0, hasProfile: true}
use-auth.ts:236 [useAuth] State changed: authenticated, ready, ok {performance: {…}, retryCount: 0, hasProfile: true}
use-auth.ts:236 [useAuth] State changed: authenticated, ready, ok {performance: {…}, retryCount: 0, hasProfile: true}
use-auth.ts:236 [useAuth] State changed: authenticated, ready, ok {performance: {…}, retryCount: 0, hasProfile: true}
use-auth.ts:236 [useAuth] State changed: authenticated, ready, ok {performance: {…}, retryCount: 0, hasProfile: true}
use-auth.ts:236 [useAuth] State changed: authenticated, ready, ok {performance: {…}, retryCount: 0, hasProfile: true}
use-auth.ts:236 [useAuth] State changed: authenticated, ready, ok {performance: {…}, retryCount: 0, hasProfile: true}
use-auth.ts:236 [useAuth] State changed: authenticated, ready, ok {performance: {…}, retryCount: 0, hasProfile: true}
use-auth.ts:236 [useAuth] State changed: authenticated, ready, ok {performance: {…}, retryCount: 0, hasProfile: true}
use-auth.ts:236 [useAuth] State changed: authenticated, ready, ok {performance: {…}, retryCount: 0, hasProfile: true}
use-user-profile-query.ts:227 [useUserProfileQuery] State change for userId 5d99e3...: {status: 'success', isLoading: false, isSuccess: true, isError: false, isStale: false, …}
use-user-profile-query.ts:227 [useUserProfileQuery] State change for userId 5d99e3...: {status: 'success', isLoading: false, isSuccess: true, isError: false, isStale: false, …}
use-user-profile-query.ts:227 [useUserProfileQuery] State change for userId 5d99e3...: {status: 'success', isLoading: false, isSuccess: true, isError: false, isStale: false, …}
use-user-profile-query.ts:227 [useUserProfileQuery] State change for userId 5d99e3...: {status: 'success', isLoading: false, isSuccess: true, isError: false, isStale: false, …}
use-user-profile-query.ts:227 [useUserProfileQuery] State change for userId 5d99e3...: {status: 'success', isLoading: false, isSuccess: true, isError: false, isStale: false, …}
use-user-profile-query.ts:227 [useUserProfileQuery] State change for userId 5d99e3...: {status: 'success', isLoading: false, isSuccess: true, isError: false, isStale: false, …}
use-user-profile-query.ts:227 [useUserProfileQuery] State change for userId 5d99e3...: {status: 'success', isLoading: false, isSuccess: true, isError: false, isStale: false, …}
use-user-profile-query.ts:227 [useUserProfileQuery] State change for userId 5d99e3...: {status: 'success', isLoading: false, isSuccess: true, isError: false, isStale: false, …}
use-user-profile-query.ts:227 [useUserProfileQuery] State change for userId 5d99e3...: {status: 'success', isLoading: false, isSuccess: true, isError: false, isStale: false, …}
use-user-profile-query.ts:227 [useUserProfileQuery] State change for userId 5d99e3...: {status: 'success', isLoading: false, isSuccess: true, isError: false, isStale: false, …}
use-user-profile-query.ts:227 [useUserProfileQuery] State change for userId 5d99e3...: {status: 'success', isLoading: false, isSuccess: true, isError: false, isStale: false, …}
use-user-profile-query.ts:227 [useUserProfileQuery] State change for userId 5d99e3...: {status: 'success', isLoading: false, isSuccess: true, isError: false, isStale: false, …}
use-user-profile-query.ts:227 [useUserProfileQuery] State change for userId 5d99e3...: {status: 'success', isLoading: false, isSuccess: true, isError: false, isStale: false, …}
use-user-profile-query.ts:227 [useUserProfileQuery] State change for userId 5d99e3...: {status: 'success', isLoading: false, isSuccess: true, isError: false, isStale: false, …}
wizard-container.tsx:105 🔄 [2025-07-15T00:04:39.610Z] WizardContainer: Syncing URL step with store: {urlStep: 'final-recipes', storeStep: 'properties'}
wizard-container.tsx:105 🔄 [2025-07-15T00:04:39.652Z] WizardContainer: Syncing URL step with store: {urlStep: 'final-recipes', storeStep: 'properties'}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: health-concern
recipe-store.ts:149 ✅ [Store Navigation] Health concern step - always allowed
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: demographics
recipe-store.ts:157 🔍 [Store Navigation] Demographics validation: {hasHealthConcern: true, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: causes
recipe-store.ts:165 🔍 [Store Navigation] Causes validation: {hasHealthConcern: true, hasDemographics: true, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: symptoms
recipe-store.ts:174 🔍 [Store Navigation] Symptoms validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: properties
recipe-store.ts:185 🔍 [Store Navigation] Properties validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: final-recipes
recipe-store.ts:203 🔍 [Store Navigation] Final Recipes validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, therapeuticPropertiesCount: 7, …}
recipe-store.ts:218 🔍 [Store Navigation] Property 1: Alívio da dor {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 2: Relaxamento muscular {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 3: Ação anti-inflamatória {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 4: Estabilização emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 5: Melhora da postura {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 6: Tonicidade {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 7: Aperfeiçoamento emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: health-concern
recipe-store.ts:149 ✅ [Store Navigation] Health concern step - always allowed
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: demographics
recipe-store.ts:157 🔍 [Store Navigation] Demographics validation: {hasHealthConcern: true, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: causes
recipe-store.ts:165 🔍 [Store Navigation] Causes validation: {hasHealthConcern: true, hasDemographics: true, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: symptoms
recipe-store.ts:174 🔍 [Store Navigation] Symptoms validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: properties
recipe-store.ts:185 🔍 [Store Navigation] Properties validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: final-recipes
recipe-store.ts:203 🔍 [Store Navigation] Final Recipes validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, therapeuticPropertiesCount: 7, …}
recipe-store.ts:218 🔍 [Store Navigation] Property 1: Alívio da dor {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 2: Relaxamento muscular {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 3: Ação anti-inflamatória {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 4: Estabilização emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 5: Melhora da postura {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 6: Tonicidade {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 7: Aperfeiçoamento emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: health-concern
recipe-store.ts:149 ✅ [Store Navigation] Health concern step - always allowed
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: demographics
recipe-store.ts:157 🔍 [Store Navigation] Demographics validation: {hasHealthConcern: true, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: causes
recipe-store.ts:165 🔍 [Store Navigation] Causes validation: {hasHealthConcern: true, hasDemographics: true, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: symptoms
recipe-store.ts:174 🔍 [Store Navigation] Symptoms validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: properties
recipe-store.ts:185 🔍 [Store Navigation] Properties validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: final-recipes
recipe-store.ts:203 🔍 [Store Navigation] Final Recipes validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, therapeuticPropertiesCount: 7, …}
recipe-store.ts:218 🔍 [Store Navigation] Property 1: Alívio da dor {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 2: Relaxamento muscular {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 3: Ação anti-inflamatória {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 4: Estabilização emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 5: Melhora da postura {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 6: Tonicidade {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 7: Aperfeiçoamento emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: health-concern
recipe-store.ts:149 ✅ [Store Navigation] Health concern step - always allowed
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: demographics
recipe-store.ts:157 🔍 [Store Navigation] Demographics validation: {hasHealthConcern: true, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: causes
recipe-store.ts:165 🔍 [Store Navigation] Causes validation: {hasHealthConcern: true, hasDemographics: true, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: symptoms
recipe-store.ts:174 🔍 [Store Navigation] Symptoms validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: properties
recipe-store.ts:185 🔍 [Store Navigation] Properties validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, result: true}
recipe-store.ts:145 🔍 [Store Navigation] Checking navigation to step: final-recipes
recipe-store.ts:203 🔍 [Store Navigation] Final Recipes validation: {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 1, selectedSymptomsCount: 1, therapeuticPropertiesCount: 7, …}
recipe-store.ts:218 🔍 [Store Navigation] Property 1: Alívio da dor {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 2: Relaxamento muscular {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 3: Ação anti-inflamatória {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 4: Estabilização emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 5: Melhora da postura {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 6: Tonicidade {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
recipe-store.ts:218 🔍 [Store Navigation] Property 7: Aperfeiçoamento emocional {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
