report-hmr-latency.ts:26 [Fast Refresh] done in 10733ms
turbopack-hot-reloader-common.ts:41 [Fast Refresh] rebuilding
report-hmr-latency.ts:26 [Fast Refresh] done in 188ms
turbopack-hot-reloader-common.ts:41 [Fast Refresh] rebuilding
report-hmr-latency.ts:26 [Fast Refresh] done in 262ms
turbopack-hot-reloader-common.ts:41 [Fast Refresh] rebuilding
report-hmr-latency.ts:26 [Fast Refresh] done in 195ms
properties-display.tsx:427 🚀 [Properties Navigation] Continue button clicked
properties-display.tsx:431 🔍 [Properties Navigation] Navigation validation: {canGoNext: true, therapeuticPropertiesCount: 5, enrichedPropertiesCount: 5, allPropertiesEnriched: true, hasHealthConcern: true, …}
properties-display.tsx:444 🔍 [Properties Navigation] Property 1: Analgésico {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
properties-display.tsx:444 🔍 [Properties Navigation] Property 2: Relaxamento muscular {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
properties-display.tsx:444 🔍 [Properties Navigation] Property 3: Sedativo {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
properties-display.tsx:444 🔍 [Properties Navigation] Property 4: Antiestresse {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
properties-display.tsx:444 🔍 [Properties Navigation] Property 5: Regulador do sono {isEnriched: true, oilsCount: 8, enrichedOilsCount: 8}
properties-display.tsx:455 🍃 [Properties Navigation] Properties step completed - Final Recipes will read from therapeuticProperties
properties-display.tsx:459 ✅ [Properties Navigation] Validation passed, navigating to next step
use-recipe-navigation.ts:239 🎯 [2025-07-15T01:25:39.706Z] goToNext called: {currentStep: 'properties', nextStep: 'final-recipes', hasNext: true}
use-recipe-navigation.ts:255 🔍 [2025-07-15T01:25:39.706Z] goToNext: Navigation validation result: true
use-recipe-navigation.ts:266 ✅ [2025-07-15T01:25:39.706Z] goToNext: Marking current step completed: properties
use-recipe-navigation.ts:269 🚀 [2025-07-15T01:25:39.706Z] goToNext: Calling goToStep with: final-recipes
use-recipe-navigation.ts:203 🚀 [2025-07-15T01:25:39.707Z] Navigation: Setting current step in store: final-recipes
use-recipe-navigation.ts:208 🌐 [2025-07-15T01:25:39.707Z] Navigation: Pushing to URL: /dashboard/create-recipe/final-recipes
wizard-container.tsx:105 🔄 [2025-07-15T01:25:39.869Z] WizardContainer: Syncing URL step with store: {urlStep: 'properties', storeStep: 'final-recipes'}
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:394 🔍 [Final Recipes] useEffect triggered with data check
final-recipes-display.tsx:405 🔍 [Final Recipes] Data validation result (UPDATED):
final-recipes-display.tsx:406 🔍 [Final Recipes] - hasHealthConcern: true
final-recipes-display.tsx:407 🔍 [Final Recipes] - hasDemographics: true
final-recipes-display.tsx:408 🔍 [Final Recipes] - selectedCausesCount: 2
final-recipes-display.tsx:409 🔍 [Final Recipes] - selectedSymptomsCount: 2
final-recipes-display.tsx:410 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils (NEW): true
final-recipes-display.tsx:411 🔍 [Final Recipes] - suggestedOils.length (OLD/PROBLEMATIC): 0
final-recipes-display.tsx:412 🔍 [Final Recipes] - hasStartedGeneration: false
final-recipes-display.tsx:413 🔍 [Final Recipes] - hasRequiredData (FINAL): true
final-recipes-display.tsx:416 ✅ [Final Recipes] Auto-starting recipe generation with required data (using therapeuticProperties)
final-recipes-display.tsx:108 🍃 [Final Recipes] handleGenerateRecipes called
final-recipes-display.tsx:148 🍃 [Final Recipes] Starting recipe generation process
final-recipes-display.tsx:149 🔍 [Final Recipes] Input data summary: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono', demographics: {…}, selectedCausesCount: 2, selectedSymptomsCount: 2, suggestedOilsCount: 0, …}
final-recipes-display.tsx:163 🔍 [Final Recipes] Selected causes: (2) [{…}, {…}]
final-recipes-display.tsx:167 🔍 [Final Recipes] Selected symptoms: (2) [{…}, {…}]
final-recipes-display.tsx:171 🔍 [Final Recipes] Therapeutic properties with oils (UPDATED - same as debug overlay): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:187 🔍 [Final Recipes] Creating minimal data structure (same as debug overlay)...
final-recipes-display.tsx:204 🔍 [Final Recipes] Extracted oils from therapeuticProperties: {totalOilsCount: 12, oilsSample: Array(3)}
final-recipes-display.tsx:217 🍃 [Final Recipes] Initiating parallel generation for time slots: (3) ['morning', 'mid-day', 'night']
final-recipes-display.tsx:231 🔍 [Final Recipes] Created propertyOilSuggestions for streaming: {propertiesCount: 5, totalOilsCount: 40, propertiesSample: Array(2)}
final-recipes-display.tsx:241 ✅ [Final Recipes] FINAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:242 ✅ [Final Recipes] - healthConcern: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono'}
final-recipes-display.tsx:243 ✅ [Final Recipes] - demographics: {gender: 'female', ageCategory: 'senior', specificAge: 68}
final-recipes-display.tsx:244 ✅ [Final Recipes] - selectedCauses count: 2
final-recipes-display.tsx:245 ✅ [Final Recipes] - selectedSymptoms count: 2
final-recipes-display.tsx:246 ✅ [Final Recipes] - propertyOilSuggestions (from therapeuticProperties): 5
final-recipes-display.tsx:247 ✅ [Final Recipes] - timeSlots with time-period integration: (3) ['morning', 'mid-day', 'night']
use-create-recipe-streaming.ts:139 🍃 [Final Recipes] Starting recipe generation for time slots: (3) ['morning', 'mid-day', 'night']
use-create-recipe-streaming.ts:140 🔍 [Final Recipes] Input validation: {timeSlots: 3, hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, …}
use-create-recipe-streaming.ts:151 🔍 [Final Recipes] Detailed input data: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono', demographics: {…}, selectedCauses: Array(2), selectedSymptoms: Array(2), suggestedOilsByProperty: Array(5)}
use-create-recipe-streaming.ts:175 🔧 [Final Recipes] Creating request for morning time slot
use-create-recipe-streaming.ts:189 🔍 [Final Recipes] Request data for morning: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'morning', dataKeys: Array(9), hasAdditionalData: false}
use-create-recipe-streaming.ts:175 🔧 [Final Recipes] Creating request for mid-day time slot
use-create-recipe-streaming.ts:189 🔍 [Final Recipes] Request data for mid-day: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'mid-day', dataKeys: Array(9), hasAdditionalData: false}
use-create-recipe-streaming.ts:175 🔧 [Final Recipes] Creating request for night time slot
use-create-recipe-streaming.ts:189 🔍 [Final Recipes] Request data for night: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'night', dataKeys: Array(9), hasAdditionalData: false}
use-create-recipe-streaming.ts:231 🍃 [Final Recipes] Initiating 3 parallel streaming requests
use-parallel-streaming-engine.ts:63 [ParallelEngine] Starting stream for ID: morning (morning recipe)
use-parallel-streaming-engine.ts:64 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
use-parallel-streaming-engine.ts:63 [ParallelEngine] Starting stream for ID: mid-day (mid-day recipe)
use-parallel-streaming-engine.ts:64 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
use-parallel-streaming-engine.ts:63 [ParallelEngine] Starting stream for ID: night (night recipe)
use-parallel-streaming-engine.ts:64 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
wizard-container.tsx:105 🔄 [2025-07-15T01:25:41.023Z] WizardContainer: Syncing URL step with store: {urlStep: 'final-recipes', storeStep: 'properties'}
final-recipes-display.tsx:394 🔍 [Final Recipes] useEffect triggered with data check
final-recipes-display.tsx:405 🔍 [Final Recipes] Data validation result (UPDATED):
final-recipes-display.tsx:406 🔍 [Final Recipes] - hasHealthConcern: true
final-recipes-display.tsx:407 🔍 [Final Recipes] - hasDemographics: true
final-recipes-display.tsx:408 🔍 [Final Recipes] - selectedCausesCount: 2
final-recipes-display.tsx:409 🔍 [Final Recipes] - selectedSymptomsCount: 2
final-recipes-display.tsx:410 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils (NEW): true
final-recipes-display.tsx:411 🔍 [Final Recipes] - suggestedOils.length (OLD/PROBLEMATIC): 0
final-recipes-display.tsx:412 🔍 [Final Recipes] - hasStartedGeneration: false
final-recipes-display.tsx:413 🔍 [Final Recipes] - hasRequiredData (FINAL): true
final-recipes-display.tsx:416 ✅ [Final Recipes] Auto-starting recipe generation with required data (using therapeuticProperties)
final-recipes-display.tsx:108 🍃 [Final Recipes] handleGenerateRecipes called
final-recipes-display.tsx:148 🍃 [Final Recipes] Starting recipe generation process
final-recipes-display.tsx:149 🔍 [Final Recipes] Input data summary: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono', demographics: {…}, selectedCausesCount: 2, selectedSymptomsCount: 2, suggestedOilsCount: 0, …}
final-recipes-display.tsx:163 🔍 [Final Recipes] Selected causes: (2) [{…}, {…}]
final-recipes-display.tsx:167 🔍 [Final Recipes] Selected symptoms: (2) [{…}, {…}]
final-recipes-display.tsx:171 🔍 [Final Recipes] Therapeutic properties with oils (UPDATED - same as debug overlay): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:187 🔍 [Final Recipes] Creating minimal data structure (same as debug overlay)...
final-recipes-display.tsx:204 🔍 [Final Recipes] Extracted oils from therapeuticProperties: {totalOilsCount: 12, oilsSample: Array(3)}
final-recipes-display.tsx:217 🍃 [Final Recipes] Initiating parallel generation for time slots: (3) ['morning', 'mid-day', 'night']
final-recipes-display.tsx:231 🔍 [Final Recipes] Created propertyOilSuggestions for streaming: {propertiesCount: 5, totalOilsCount: 40, propertiesSample: Array(2)}
final-recipes-display.tsx:241 ✅ [Final Recipes] FINAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:242 ✅ [Final Recipes] - healthConcern: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono'}
final-recipes-display.tsx:243 ✅ [Final Recipes] - demographics: {gender: 'female', ageCategory: 'senior', specificAge: 68}
final-recipes-display.tsx:244 ✅ [Final Recipes] - selectedCauses count: 2
final-recipes-display.tsx:245 ✅ [Final Recipes] - selectedSymptoms count: 2
final-recipes-display.tsx:246 ✅ [Final Recipes] - propertyOilSuggestions (from therapeuticProperties): 5
final-recipes-display.tsx:247 ✅ [Final Recipes] - timeSlots with time-period integration: (3) ['morning', 'mid-day', 'night']
use-create-recipe-streaming.ts:139 🍃 [Final Recipes] Starting recipe generation for time slots: (3) ['morning', 'mid-day', 'night']
use-create-recipe-streaming.ts:140 🔍 [Final Recipes] Input validation: {timeSlots: 3, hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, …}
use-create-recipe-streaming.ts:151 🔍 [Final Recipes] Detailed input data: {healthConcern: 'Tenho dores de cabeça crônicas que pioram com estresse e falta de sono', demographics: {…}, selectedCauses: Array(2), selectedSymptoms: Array(2), suggestedOilsByProperty: Array(5)}
use-create-recipe-streaming.ts:175 🔧 [Final Recipes] Creating request for morning time slot
use-create-recipe-streaming.ts:189 🔍 [Final Recipes] Request data for morning: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'morning', dataKeys: Array(9), hasAdditionalData: false}
use-create-recipe-streaming.ts:175 🔧 [Final Recipes] Creating request for mid-day time slot
use-create-recipe-streaming.ts:189 🔍 [Final Recipes] Request data for mid-day: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'mid-day', dataKeys: Array(9), hasAdditionalData: false}
use-create-recipe-streaming.ts:175 🔧 [Final Recipes] Creating request for night time slot
use-create-recipe-streaming.ts:189 🔍 [Final Recipes] Request data for night: {feature: 'create-recipe', step: 'final-recipes', timeSlot: 'night', dataKeys: Array(9), hasAdditionalData: false}
use-create-recipe-streaming.ts:231 🍃 [Final Recipes] Initiating 3 parallel streaming requests
use-parallel-streaming-engine.ts:63 [ParallelEngine] Starting stream for ID: morning (morning recipe)
use-parallel-streaming-engine.ts:64 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
use-parallel-streaming-engine.ts:63 [ParallelEngine] Starting stream for ID: mid-day (mid-day recipe)
use-parallel-streaming-engine.ts:64 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
use-parallel-streaming-engine.ts:63 [ParallelEngine] Starting stream for ID: night (night recipe)
use-parallel-streaming-engine.ts:64 [ParallelEngine] Request data: {feature: 'create-recipe', step: 'final-recipes', dataKeys: Array(9)}
wizard-container.tsx:105 🔄 [2025-07-15T01:25:41.159Z] WizardContainer: Syncing URL step with store: {urlStep: 'final-recipes', storeStep: 'properties'}
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:394 🔍 [Final Recipes] useEffect triggered with data check
final-recipes-display.tsx:405 🔍 [Final Recipes] Data validation result (UPDATED):
final-recipes-display.tsx:406 🔍 [Final Recipes] - hasHealthConcern: true
final-recipes-display.tsx:407 🔍 [Final Recipes] - hasDemographics: true
final-recipes-display.tsx:408 🔍 [Final Recipes] - selectedCausesCount: 2
final-recipes-display.tsx:409 🔍 [Final Recipes] - selectedSymptomsCount: 2
final-recipes-display.tsx:410 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils (NEW): true
final-recipes-display.tsx:411 🔍 [Final Recipes] - suggestedOils.length (OLD/PROBLEMATIC): 0
final-recipes-display.tsx:412 🔍 [Final Recipes] - hasStartedGeneration: true
final-recipes-display.tsx:413 🔍 [Final Recipes] - hasRequiredData (FINAL): true
final-recipes-display.tsx:419 🔍 [Final Recipes] Not starting generation: {reason: 'Already started generation', missingData: {…}}
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
turbopack-hot-reloader-common.ts:41 [Fast Refresh] rebuilding
report-hmr-latency.ts:26 [Fast Refresh] done in 180ms
turbopack-hot-reloader-common.ts:41 [Fast Refresh] rebuilding
report-hmr-latency.ts:26 [Fast Refresh] done in 266ms
turbopack-hot-reloader-common.ts:41 [Fast Refresh] rebuilding
report-hmr-latency.ts:26 [Fast Refresh] done in 218ms
turbopack-hot-reloader-common.ts:41 [Fast Refresh] rebuilding
report-hmr-latency.ts:26 [Fast Refresh] done in 536ms
turbopack-hot-reloader-common.ts:41 [Fast Refresh] rebuilding
use-create-recipe-streaming.ts:244 🔍 [Final Recipes] Promise status check: {timestamp: '2025-07-15T01:25:45.971Z', promises: Array(3)}
use-create-recipe-streaming.ts:244 🔍 [Final Recipes] Promise status check: {timestamp: '2025-07-15T01:25:46.120Z', promises: Array(3)}
use-create-recipe-streaming.ts:244 🔍 [Final Recipes] Promise status check: {timestamp: '2025-07-15T01:25:50.970Z', promises: Array(3)}
use-create-recipe-streaming.ts:244 🔍 [Final Recipes] Promise status check: {timestamp: '2025-07-15T01:25:51.118Z', promises: Array(3)}
use-create-recipe-streaming.ts:203 🔍 [Final Recipes] Response parser called for night: {hasFinalData: true, hasData: true, hasRecipeProtocol: true, updateKeys: Array(1)}
use-create-recipe-streaming.ts:213 ✅ [Final Recipes] Successfully parsed night recipe: {recipeName: 'Óleo Relaxante Noturno', selectedOilsCount: 5, preparationStepsCount: 0, usageInstructionsCount: 0, hasContainerInfo: false}
use-parallel-streaming-engine.ts:74 [ParallelEngine] Parsed result for night: SUCCESS
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for night to finalResults. Size now: 1
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for night to finalResults. Size now: 1
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
report-hmr-latency.ts:26 [Fast Refresh] done in 10283ms
turbopack-hot-reloader-common.ts:41 [Fast Refresh] rebuilding
streaming.service.ts:152 Stream completed naturally
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for night to finalResults. Size now: 1
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for night to finalResults. Size now: 1
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
use-create-recipe-streaming.ts:244 🔍 [Final Recipes] Promise status check: {timestamp: '2025-07-15T01:25:55.973Z', promises: Array(3)}
use-create-recipe-streaming.ts:244 🔍 [Final Recipes] Promise status check: {timestamp: '2025-07-15T01:25:56.118Z', promises: Array(3)}
use-create-recipe-streaming.ts:203 🔍 [Final Recipes] Response parser called for mid-day: {hasFinalData: true, hasData: true, hasRecipeProtocol: true, updateKeys: Array(1)}
use-create-recipe-streaming.ts:213 ✅ [Final Recipes] Successfully parsed mid-day recipe: {recipeName: 'Mistura Relaxante Noturna para Dores de Cabeça Crônicas', selectedOilsCount: 5, preparationStepsCount: 0, usageInstructionsCount: 0, hasContainerInfo: false}
use-parallel-streaming-engine.ts:74 [ParallelEngine] Parsed result for mid-day: SUCCESS
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for mid-day to finalResults. Size now: 1
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for mid-day to finalResults. Size now: 1
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
streaming.service.ts:152 Stream completed naturally
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for mid-day to finalResults. Size now: 1
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for mid-day to finalResults. Size now: 1
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
use-create-recipe-streaming.ts:203 🔍 [Final Recipes] Response parser called for morning: {hasFinalData: true, hasData: true, hasRecipeProtocol: true, updateKeys: Array(1)}
use-create-recipe-streaming.ts:213 ✅ [Final Recipes] Successfully parsed morning recipe: {recipeName: 'Blend Relaxante Para Dores de Cabeça Crônicas', selectedOilsCount: 4, preparationStepsCount: 0, usageInstructionsCount: 0, hasContainerInfo: false}
use-parallel-streaming-engine.ts:74 [ParallelEngine] Parsed result for morning: SUCCESS
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for morning to finalResults. Size now: 2
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for morning to finalResults. Size now: 2
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
streaming.service.ts:152 Stream completed naturally
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for morning to finalResults. Size now: 2
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for morning to finalResults. Size now: 2
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
use-create-recipe-streaming.ts:203 🔍 [Final Recipes] Response parser called for mid-day: {hasFinalData: true, hasData: true, hasRecipeProtocol: true, updateKeys: Array(1)}
use-create-recipe-streaming.ts:213 ✅ [Final Recipes] Successfully parsed mid-day recipe: {recipeName: 'Ritual Calmante Noturno para Dores de Cabeça Crônicas', selectedOilsCount: 4, preparationStepsCount: 0, usageInstructionsCount: 0, hasContainerInfo: false}
use-parallel-streaming-engine.ts:74 [ParallelEngine] Parsed result for mid-day: SUCCESS
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for mid-day to finalResults. Size now: 2
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for mid-day to finalResults. Size now: 2
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
streaming.service.ts:152 Stream completed naturally
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for mid-day to finalResults. Size now: 2
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for mid-day to finalResults. Size now: 2
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
use-create-recipe-streaming.ts:203 🔍 [Final Recipes] Response parser called for night: {hasFinalData: true, hasData: true, hasRecipeProtocol: true, updateKeys: Array(1)}
use-create-recipe-streaming.ts:213 ✅ [Final Recipes] Successfully parsed night recipe: {recipeName: 'Revitalização Matinal para Dores de Cabeça', selectedOilsCount: 3, preparationStepsCount: 0, usageInstructionsCount: 0, hasContainerInfo: false}
use-parallel-streaming-engine.ts:74 [ParallelEngine] Parsed result for night: SUCCESS
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for night to finalResults. Size now: 3
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for night to finalResults. Size now: 3
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
streaming.service.ts:152 Stream completed naturally
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for night to finalResults. Size now: 3
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for night to finalResults. Size now: 3
use-parallel-streaming-engine.ts:115 [ParallelEngine] All streams completed. Results: 3/3
use-parallel-streaming-engine.ts:116 [ParallelEngine] Final results keys: (3) ['mid-day', 'morning', 'night']
use-create-recipe-streaming.ts:292 ✅ [Final Recipes] All recipe generation completed
use-create-recipe-streaming.ts:293 🔍 [Final Recipes] Generation results: {totalDurationMs: 18453, totalDurationSeconds: 18, resultsCount: 3, expectedCount: 3, successfulSlots: Array(3), …}
use-create-recipe-streaming.ts:303 🔍 [Final Recipes] Promise tracking summary: {finalStates: Array(3)}
use-create-recipe-streaming.ts:317 ✅ [Final Recipes] mid-day recipe generation successful: {duration: 18444, status: 'resolved'}
use-create-recipe-streaming.ts:317 ✅ [Final Recipes] morning recipe generation successful: {duration: 18444, status: 'resolved'}
use-create-recipe-streaming.ts:317 ✅ [Final Recipes] night recipe generation successful: {duration: 18444, status: 'resolved'}
final-recipes-display.tsx:260 🍃 [Final Recipes] Recipe generation completed
final-recipes-display.tsx:261 🔍 [Final Recipes] Generation timing: {totalDurationMs: 18458, totalDurationSeconds: 18, resultsCount: 3, expectedCount: 3}
final-recipes-display.tsx:270 🍃 [Final Recipes] Processing individual recipe results:
final-recipes-display.tsx:273 ✅ [Final Recipes] mid-day recipe generated successfully: {recipeName: 'Mistura Relaxante Noturna para Dores de Cabeça Crônicas', selectedOilsCount: 5, preparationStepsCount: 0, usageInstructionsCount: 0}
final-recipes-display.tsx:273 ✅ [Final Recipes] morning recipe generated successfully: {recipeName: 'Blend Relaxante Para Dores de Cabeça Crônicas', selectedOilsCount: 4, preparationStepsCount: 0, usageInstructionsCount: 0}
final-recipes-display.tsx:273 ✅ [Final Recipes] night recipe generated successfully: {recipeName: 'Revitalização Matinal para Dores de Cabeça', selectedOilsCount: 3, preparationStepsCount: 0, usageInstructionsCount: 0}
final-recipes-display.tsx:287 🍃 [Final Recipes] Completing AI streaming with results
use-batched-recipe-updates.ts:173 🍃 [Final Recipes] Processing recipe results: 3
use-batched-recipe-updates.ts:178 ✅ [Final Recipes] Updating mid-day recipe in store
use-batched-recipe-updates.ts:178 ✅ [Final Recipes] Updating morning recipe in store
use-batched-recipe-updates.ts:178 ✅ [Final Recipes] Updating night recipe in store
use-batched-recipe-updates.ts:186 🍃 [Final Recipes] Resetting generation flag after completion
use-batched-recipe-updates.ts:197 🔄 [2025-07-15T01:25:59.560Z] [useBatchedRecipeUpdates] Batching all updates for final-recipes completion
use-batched-recipe-updates.ts:199 📋 [2025-07-15T01:25:59.560Z] Streaming updates: 2 updates
use-batched-recipe-updates.ts:200 📋 [2025-07-15T01:25:59.560Z] Step updates: 0 updates
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:394 🔍 [Final Recipes] useEffect triggered with data check
final-recipes-display.tsx:405 🔍 [Final Recipes] Data validation result (UPDATED):
final-recipes-display.tsx:406 🔍 [Final Recipes] - hasHealthConcern: true
final-recipes-display.tsx:407 🔍 [Final Recipes] - hasDemographics: true
final-recipes-display.tsx:408 🔍 [Final Recipes] - selectedCausesCount: 2
final-recipes-display.tsx:409 🔍 [Final Recipes] - selectedSymptomsCount: 2
final-recipes-display.tsx:410 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils (NEW): true
final-recipes-display.tsx:411 🔍 [Final Recipes] - suggestedOils.length (OLD/PROBLEMATIC): 0
final-recipes-display.tsx:412 🔍 [Final Recipes] - hasStartedGeneration: true
final-recipes-display.tsx:413 🔍 [Final Recipes] - hasRequiredData (FINAL): true
final-recipes-display.tsx:419 🔍 [Final Recipes] Not starting generation: {reason: 'Already started generation', missingData: {…}}
use-create-recipe-streaming.ts:203 🔍 [Final Recipes] Response parser called for morning: {hasFinalData: true, hasData: true, hasRecipeProtocol: true, updateKeys: Array(1)}
use-create-recipe-streaming.ts:213 ✅ [Final Recipes] Successfully parsed morning recipe: {recipeName: 'Mistura Relaxante Noturna para Dores de Cabeça', selectedOilsCount: 5, preparationStepsCount: 0, usageInstructionsCount: 0, hasContainerInfo: false}
use-parallel-streaming-engine.ts:74 [ParallelEngine] Parsed result for morning: SUCCESS
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for morning to finalResults. Size now: 3
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for morning to finalResults. Size now: 3
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
streaming.service.ts:152 Stream completed naturally
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for morning to finalResults. Size now: 3
use-parallel-streaming-engine.ts:90 [ParallelEngine] Added result for morning to finalResults. Size now: 3
use-parallel-streaming-engine.ts:115 [ParallelEngine] All streams completed. Results: 3/3
use-parallel-streaming-engine.ts:116 [ParallelEngine] Final results keys: (3) ['night', 'mid-day', 'morning']
use-create-recipe-streaming.ts:292 ✅ [Final Recipes] All recipe generation completed
use-create-recipe-streaming.ts:293 🔍 [Final Recipes] Generation results: {totalDurationMs: 19135, totalDurationSeconds: 19, resultsCount: 3, expectedCount: 3, successfulSlots: Array(3), …}
use-create-recipe-streaming.ts:303 🔍 [Final Recipes] Promise tracking summary: {finalStates: Array(3)}
use-create-recipe-streaming.ts:317 ✅ [Final Recipes] night recipe generation successful: {duration: 19125, status: 'resolved'}
use-create-recipe-streaming.ts:317 ✅ [Final Recipes] mid-day recipe generation successful: {duration: 19125, status: 'resolved'}
use-create-recipe-streaming.ts:317 ✅ [Final Recipes] morning recipe generation successful: {duration: 19125, status: 'resolved'}
final-recipes-display.tsx:260 🍃 [Final Recipes] Recipe generation completed
final-recipes-display.tsx:261 🔍 [Final Recipes] Generation timing: {totalDurationMs: 19143, totalDurationSeconds: 19, resultsCount: 3, expectedCount: 3}
final-recipes-display.tsx:270 🍃 [Final Recipes] Processing individual recipe results:
final-recipes-display.tsx:273 ✅ [Final Recipes] night recipe generated successfully: {recipeName: 'Óleo Relaxante Noturno', selectedOilsCount: 5, preparationStepsCount: 0, usageInstructionsCount: 0}
final-recipes-display.tsx:273 ✅ [Final Recipes] mid-day recipe generated successfully: {recipeName: 'Ritual Calmante Noturno para Dores de Cabeça Crônicas', selectedOilsCount: 4, preparationStepsCount: 0, usageInstructionsCount: 0}
final-recipes-display.tsx:273 ✅ [Final Recipes] morning recipe generated successfully: {recipeName: 'Mistura Relaxante Noturna para Dores de Cabeça', selectedOilsCount: 5, preparationStepsCount: 0, usageInstructionsCount: 0}
final-recipes-display.tsx:287 🍃 [Final Recipes] Completing AI streaming with results
use-batched-recipe-updates.ts:173 🍃 [Final Recipes] Processing recipe results: 3
use-batched-recipe-updates.ts:178 ✅ [Final Recipes] Updating night recipe in store
use-batched-recipe-updates.ts:178 ✅ [Final Recipes] Updating mid-day recipe in store
use-batched-recipe-updates.ts:178 ✅ [Final Recipes] Updating morning recipe in store
use-batched-recipe-updates.ts:186 🍃 [Final Recipes] Resetting generation flag after completion
use-batched-recipe-updates.ts:197 🔄 [2025-07-15T01:26:00.096Z] [useBatchedRecipeUpdates] Batching all updates for final-recipes completion
use-batched-recipe-updates.ts:199 📋 [2025-07-15T01:26:00.096Z] Streaming updates: 2 updates
use-batched-recipe-updates.ts:200 📋 [2025-07-15T01:26:00.096Z] Step updates: 0 updates
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:394 🔍 [Final Recipes] useEffect triggered with data check
final-recipes-display.tsx:405 🔍 [Final Recipes] Data validation result (UPDATED):
final-recipes-display.tsx:406 🔍 [Final Recipes] - hasHealthConcern: true
final-recipes-display.tsx:407 🔍 [Final Recipes] - hasDemographics: true
final-recipes-display.tsx:408 🔍 [Final Recipes] - selectedCausesCount: 2
final-recipes-display.tsx:409 🔍 [Final Recipes] - selectedSymptomsCount: 2
final-recipes-display.tsx:410 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils (NEW): true
final-recipes-display.tsx:411 🔍 [Final Recipes] - suggestedOils.length (OLD/PROBLEMATIC): 0
final-recipes-display.tsx:412 🔍 [Final Recipes] - hasStartedGeneration: true
final-recipes-display.tsx:413 🔍 [Final Recipes] - hasRequiredData (FINAL): true
final-recipes-display.tsx:419 🔍 [Final Recipes] Not starting generation: {reason: 'Already started generation', missingData: {…}}
report-hmr-latency.ts:26 [Fast Refresh] done in 6693ms
turbopack-hot-reloader-common.ts:41 [Fast Refresh] rebuilding
report-hmr-latency.ts:26 [Fast Refresh] done in 120ms
turbopack-hot-reloader-common.ts:41 [Fast Refresh] rebuilding
report-hmr-latency.ts:26 [Fast Refresh] done in 121ms
turbopack-hot-reloader-common.ts:41 [Fast Refresh] rebuilding
report-hmr-latency.ts:26 [Fast Refresh] done in 177ms
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
use-create-recipe-streaming.ts:260 ❌ [Final Recipes] Timeout detected for morning after 45000ms
error @ intercept-console-error.ts:40
useCreateRecipeStreaming.useCallback[startFinalRecipesStreaming].timeoutPromises @ use-create-recipe-streaming.ts:260
use-create-recipe-streaming.ts:260 ❌ [Final Recipes] Timeout detected for mid-day after 45000ms
error @ intercept-console-error.ts:40
useCreateRecipeStreaming.useCallback[startFinalRecipesStreaming].timeoutPromises @ use-create-recipe-streaming.ts:260
use-create-recipe-streaming.ts:260 ❌ [Final Recipes] Timeout detected for night after 45000ms
error @ intercept-console-error.ts:40
useCreateRecipeStreaming.useCallback[startFinalRecipesStreaming].timeoutPromises @ use-create-recipe-streaming.ts:260
use-create-recipe-streaming.ts:260 ❌ [Final Recipes] Timeout detected for morning after 45000ms
error @ intercept-console-error.ts:40
useCreateRecipeStreaming.useCallback[startFinalRecipesStreaming].timeoutPromises @ use-create-recipe-streaming.ts:260
use-create-recipe-streaming.ts:260 ❌ [Final Recipes] Timeout detected for mid-day after 45000ms
error @ intercept-console-error.ts:40
useCreateRecipeStreaming.useCallback[startFinalRecipesStreaming].timeoutPromises @ use-create-recipe-streaming.ts:260
use-create-recipe-streaming.ts:260 ❌ [Final Recipes] Timeout detected for night after 45000ms
error @ intercept-console-error.ts:40
useCreateRecipeStreaming.useCallback[startFinalRecipesStreaming].timeoutPromises @ use-create-recipe-streaming.ts:260
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
final-recipes-display.tsx:28 🍃 [Final Recipes] Component mounting/rendering
final-recipes-display.tsx:48 🔍 [Final Recipes] Store state on render (same as debug overlay): {hasHealthConcern: true, hasDemographics: true, selectedCausesCount: 2, selectedSymptomsCount: 2, therapeuticPropertiesCount: 5, …}
final-recipes-display.tsx:66 🔍 [Final Recipes] ACTUAL DATA STRUCTURE (same as debug overlay):
final-recipes-display.tsx:67 🔍 [Final Recipes] therapeuticProperties (main data source): (5) [{…}, {…}, {…}, {…}, {…}]
final-recipes-display.tsx:86 🔍 [Final Recipes] Data availability analysis:
final-recipes-display.tsx:87 🔍 [Final Recipes] - hasTherapeuticPropertiesWithOils: true
final-recipes-display.tsx:88 🔍 [Final Recipes] - suggestedOils.length (problematic): 0
final-recipes-display.tsx:89 🔍 [Final Recipes] - Total oils across all properties: 40
final-recipes-display.tsx:94 ❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!
final-recipes-display.tsx:95 ❌ [Final Recipes] This confirms that Properties step is not populating the suggestedOils array
final-recipes-display.tsx:96 ✅ [Final Recipes] SOLUTION: Use therapeuticProperties directly (same as debug overlay)
