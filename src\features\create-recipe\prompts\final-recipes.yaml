version: "1.0.0"
description: "AI agent for generating personalized essential oil recipes for specific time slots"
config:
  model: "gpt-4.1-nano"
  temperature: 0.3
  max_tokens: 4000
  response_format: "json_schema"
  timeout_seconds: 90

user_message: |
  ## Input Data
  1. `health_concern`: {{health_concern}}
  2. `demographics`:
     - Gender: {{gender}}
     - Age Category: {{age_category}}
     - Specific Age: {{specific_age}} years old
  3. `selected_causes`:
     {{#each selected_causes}}
     - Cause ID: {{cause_id}}
     - Name: {{cause_name}}
     - Explanation: {{explanation}}
     {{/each}}
  4. `selected_symptoms`:
     {{#each selected_symptoms}}
     - Symptom ID: {{symptom_id}}
     - Name: {{symptom_name}}
     - Explanation: {{explanation}}
     {{/each}}
  5. `suggested_oils`:
     {{#each suggested_oils}}
     - Oil ID: {{oil_id}}
     - English Name: {{name_english}}
     - Botanical Name: {{name_botanical}}
     - Localized Name: {{name_localized}}
     - Safety Data: {{safety}}
     {{/each}}
  6. `time_of_day`: {{time_of_day}}

template: |
  # Essential Oil Recipe Generator Agent

  You are an expert aromatherapist specializing in creating safe, personalized essential oil protocols. 
  Your task is to generate ONE recipe for the specified time of day using the provided oils and user profile.

  ## Core Directives (NON-NEGOTIABLE)
  1. **Safety First**: Filter out any oils that are unsafe for the user's age and demographics
  2. **Time-Appropriate Selection**: Choose oils suitable for the specified time of day
  3. **Conservative Dilution**: Use age-appropriate dilution ratios (0.5-1% for children, 1-2% for adults)
  4. **Practical Application**: Provide clear, actionable instructions
  5. **Container Sizing**: Calculate container size based on usage frequency and application area

  ## Safety Filtering Rules
  - **Children under 10**: Exclude dermocaustic oils and use maximum 0.5% dilution
  - **Pregnancy/Nursing**: Check pregnancy_nursing safety data if applicable
  - **Phototoxicity**: Include warnings for citrus oils if topical application

  ## Time-of-Day Guidelines
  - **Morning**: Energizing, uplifting oils (citrus, peppermint, rosemary)
  - **Mid-day**: Balancing, focusing oils (frankincense, lavender, eucalyptus)
  - **Night**: Calming, relaxing oils (lavender, cedarwood, chamomile)

  ## Recipe Generation Process
  1. **Filter Safe Oils**: Remove any oils unsafe for user demographics
  2. **Select 3-5 Oils**: Choose oils appropriate for time of day and health concern
  3. **Calculate Dilution**: Use conservative ratios based on age
  4. **Determine Container**: Size based on drops per application × frequency × duration
  5. **Generate Instructions**: Clear, step-by-step preparation and usage

  ## Container Sizing Logic
  - Face application: 5ml roller bottle (smaller area, frequent use)
  - Body application: 10ml roller bottle (larger area, moderate use)
  - Diffuser blend: 5ml dropper bottle (aromatic use only)
  - Calculate: (drops per use × uses per day × days of use) ÷ 20 drops per ml

  ## Output Requirements
  - Recipe name in user's language
  - 3-5 selected oils with drop counts
  - Carrier oil recommendation
  - Container recommendation with rationale
  - Safety warnings specific to selected oils
  - Preparation and usage instructions
  - Application method and frequency

schema:
  type: "json_schema"
  name: "final_recipe_response"
  strict: true
  schema:
    type: "object"
    properties:
      meta:
        type: "object"
        properties:
          step_name:
            type: "string"
            description: "The name of the step."
          request_id:
            type: "string"
            format: "uuid"
            description: "Unique identifier for the request."
          timestamp_utc:
            type: "string"
            format: "date-time"
            description: "Timestamp of the response in UTC."
          version:
            type: "string"
            description: "Version of the API."
          user_language:
            type: "string"
            description: "Language preference of the user."
          status:
            type: "string"
            description: "Status of the response."
          message:
            type: "string"
            description: "Descriptive message about the response."
        required: ["step_name", "request_id", "timestamp_utc", "version", "user_language", "status", "message"]
        additionalProperties: false
      data:
        type: "object"
        properties:
          recipe_protocol:
            type: "object"
            properties:
              recipe_id:
                type: "string"
                format: "uuid"
                description: "Unique identifier for this recipe."
              time_slot:
                type: "string"
                enum: ["morning", "mid-day", "night"]
                description: "Time of day for this recipe."
              recipe_name_localized:
                type: "string"
                description: "Localized name for the recipe."
              description_localized:
                type: "string"
                description: "Brief description of the recipe's purpose."
              selected_oils:
                type: "array"
                minItems: 3
                maxItems: 5
                items:
                  type: "object"
                  properties:
                    oil_id:
                      type: "string"
                      format: "uuid"
                    name_localized:
                      type: "string"
                    name_botanical:
                      type: "string"
                    drops_count:
                      type: "integer"
                      minimum: 1
                      maximum: 10
                    rationale_localized:
                      type: "string"
                      description: "Why this oil was selected for this time of day."
                  required: ["oil_id", "name_localized", "name_botanical", "drops_count", "rationale_localized"]
                  additionalProperties: false
              carrier_oil:
                type: "object"
                properties:
                  name_localized:
                    type: "string"
                  amount_ml:
                    type: "number"
                    minimum: 5
                    maximum: 30
                required: ["name_localized", "amount_ml"]
                additionalProperties: false
              total_drops:
                type: "integer"
                description: "Total essential oil drops in recipe."
              total_volume_ml:
                type: "number"
                description: "Total volume including carrier oil."
              application_method_localized:
                type: "string"
                description: "How to apply the blend (topical, aromatic, etc.)."
              frequency_localized:
                type: "string"
                description: "How often to use (e.g., 'twice daily')."
              duration_localized:
                type: "string"
                description: "How long to use (e.g., '2-3 weeks')."
              container_recommendation:
                type: "object"
                properties:
                  size_ml:
                    type: "integer"
                    enum: [5, 10, 15, 30]
                  container_type:
                    type: "string"
                    enum: ["roller_bottle", "dropper_bottle", "spray_bottle", "jar"]
                  material:
                    type: "string"
                    enum: ["amber_glass", "clear_glass", "plastic"]
                  dispenser_type:
                    type: "string"
                    enum: ["roller", "dropper", "spray", "none"]
                  rationale_localized:
                    type: "string"
                required: ["size_ml", "container_type", "material", "dispenser_type", "rationale_localized"]
                additionalProperties: false
              safety_warnings:
                type: "array"
                items:
                  type: "object"
                  properties:
                    warning_type:
                      type: "string"
                      enum: ["age_restriction", "pregnancy", "phototoxicity", "dilution", "general"]
                    severity:
                      type: "string"
                      enum: ["low", "medium", "high"]
                    message_localized:
                      type: "string"
                    guidance_localized:
                      type: "string"
                  required: ["warning_type", "severity", "message_localized", "guidance_localized"]
                  additionalProperties: false
              preparation_steps_localized:
                type: "array"
                items:
                  type: "string"
                description: "Step-by-step preparation instructions."
              usage_instructions_localized:
                type: "array"
                items:
                  type: "string"
                description: "Step-by-step usage instructions."
            required: [
              "recipe_id", "time_slot", "recipe_name_localized", "description_localized",
              "selected_oils", "carrier_oil", "total_drops", "total_volume_ml",
              "application_method_localized", "frequency_localized", "duration_localized",
              "container_recommendation", "safety_warnings", "preparation_steps_localized",
              "usage_instructions_localized"
            ]
            additionalProperties: false
        required: ["recipe_protocol"]
        additionalProperties: false
      echo:
        type: "object"
        properties:
          health_concern_input:
            type: "string"
          user_info_input:
            type: "object"
            properties:
              gender: { type: "string" }
              age_category: { type: "string" }
              specific_age: { type: "integer" }
            required: ["gender", "age_category", "specific_age"]
            additionalProperties: false
          selected_cause_ids:
            type: "array"
            items: { type: "string" }
          selected_symptom_ids:
            type: "array"
            items: { type: "string" }
          time_of_day:
            type: "string"
            enum: ["morning", "mid-day", "night"]
        required: ["health_concern_input", "user_info_input", "selected_cause_ids", "selected_symptom_ids", "time_of_day"]
        additionalProperties: false
    required: ["meta", "data", "echo"]
    additionalProperties: false
