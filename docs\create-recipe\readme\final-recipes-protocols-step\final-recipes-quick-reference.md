# Final Recipes Feature - Quick Reference Guide

## 🚀 Quick Start

### Component Usage
```typescript
import { FinalRecipesDisplay } from '@/features/create-recipe/components/final-recipes-display';

// The component auto-starts recipe generation when mounted with required data
<FinalRecipesDisplay />
```

### Store Integration
```typescript
import { useRecipeStore } from '@/features/create-recipe/store/recipe-store';

const { finalRecipes, isStreamingFinalRecipes } = useRecipeStore();
```

## 📁 File Structure

```
src/features/create-recipe/components/
├── final-recipes-display.tsx      # Main container with tabs
├── protocol-summary-card.tsx      # 3D flip cards for overview
├── recipe-protocol-card.tsx       # Detailed recipe display
└── safety-warnings.tsx            # Age-appropriate safety warnings

src/lib/i18n/messages/
├── en/create-recipe.json          # English translations
├── pt/create-recipe.json          # Portuguese translations
└── es/create-recipe.json          # Spanish translations
```

## 🎯 Key Features

### Tab Navigation
- **Overview**: User profile + therapeutic strategy + protocol summary
- **Recipes**: Timeline navigation + detailed protocol cards
- **Studies**: Scientific references (placeholder)
- **Security**: Safety warnings + guidelines

### Protocol Cards
- **Summary Cards**: 3D flip animation with quick overview
- **Detailed Cards**: Collapsible sections (Usage, Preparation, Science)
- **Time-Specific**: Morning (slate), Mid-day (orange), Night (indigo)

### Safety Integration
- **Age-Based**: Special handling for children under 10
- **Severity Levels**: High (red), Medium (yellow), Low (blue)
- **Dynamic Content**: Adapts to user demographics

## 🔧 Technical Integration

### Data Flow
1. **Properties Step**: Oil enrichment completes → Continue button enables
2. **Navigation**: User clicks Continue → navigates to `/dashboard/create-recipe/final-recipes`
3. **Component Mount**: Auto-detects required wizard data from store
4. **AI Generation**: Triggers parallel generation for 3 time slots (morning/mid-day/night)
5. **Store Updates**: Individual recipe results update store state
6. **UI Rendering**: Completed recipes render in tabbed interface

### State Management
```typescript
interface FinalRecipesState {
  morning: { recipe: FinalRecipeProtocol | null; status: RecipeStatus };
  midDay: { recipe: FinalRecipeProtocol | null; status: RecipeStatus };
  night: { recipe: FinalRecipeProtocol | null; status: RecipeStatus };
  isGenerating: boolean;
  hasStartedGeneration: boolean;
  globalError: string | null;
}
```

### AI Integration
- **Endpoint**: `/api/ai/streaming`
- **Model**: `gpt-4.1-nano`
- **Processing**: Parallel generation for 3 time slots
- **Error Handling**: Individual recipe failure recovery

## 🌍 Internationalization

### Translation Keys
```typescript
// Tab labels
t('create-recipe:steps.final-recipes.tabs.overview')
t('create-recipe:steps.final-recipes.tabs.recipes')

// Protocol information
t('create-recipe:steps.final-recipes.protocols.morning.label')
t('create-recipe:steps.final-recipes.protocols.midDay.purpose')

// Safety warnings
t('create-recipe:steps.final-recipes.safety.guidelines.beforeUse.title')
```

### Supported Languages
- **English (en)**: Base language
- **Portuguese (pt)**: Brazilian Portuguese
- **Spanish (es)**: Latin American Spanish

## 📱 Responsive Design

### Breakpoints
- **Mobile**: Single column layout, touch-optimized
- **Tablet (md)**: 2-3 column grids
- **Desktop (lg)**: Full layout with sidebar navigation

### Mobile Optimizations
- Touch targets minimum 44px
- Horizontal tab scrolling
- Optimized typography scaling
- Efficient grid stacking

## 🎨 Styling & Animation

### 3D Flip Cards
```css
.perspective-1000 { perspective: 1000px; }
.transform-style-preserve-3d { transform-style: preserve-3d; }
.backface-hidden { backface-visibility: hidden; }
.rotate-y-180 { transform: rotateY(180deg); }
```

### Color Schemes
- **Morning**: Slate gradient (`from-slate-800 to-slate-900`)
- **Mid-day**: Orange gradient (`from-yellow-400 to-orange-500`)
- **Night**: Indigo gradient (`from-indigo-500 to-indigo-900`)

## 🔒 Safety & Security

### Age-Based Filtering
```typescript
if (demographics.specificAge < 10) {
  // Apply child-safe restrictions
  // Maximum 0.5% dilution
  // Exclude dermocaustic oils
}
```

### Content Validation
- Input sanitization before AI processing
- JSON schema validation for responses
- Type safety with comprehensive interfaces
- Medical disclaimers and professional guidance

## 🚨 Error Handling

### Common Scenarios
- **Missing Data**: Validation before generation
- **AI Failures**: Individual recipe retry logic
- **Network Issues**: Graceful degradation
- **Partial Success**: Display available recipes

### Error Recovery
```typescript
try {
  const results = await startFinalRecipesStreaming(...);
  completeAIStreaming('final-recipes', results);
} catch (error) {
  console.error('Failed to generate final recipes:', error);
  completeAIStreaming('final-recipes', new Map());
}
```

## 🔍 Debugging

### Console Logging
```typescript
console.log('🍃 [Final Recipes] Starting recipe generation process');
console.log(`✅ [Final Recipes] Successfully parsed ${timeSlot} recipe`);
console.error('Failed to generate final recipes:', error);
```

### Store Inspection
```typescript
// Access store state for debugging
const store = useRecipeStore.getState();
console.log('Final Recipes State:', store.finalRecipes);
```

## 📊 Performance

### Optimization Strategies
- **Lazy Loading**: Components load progressively
- **Memoization**: React.memo for expensive components
- **Efficient Re-renders**: Selective state subscriptions
- **CSS Animations**: Hardware-accelerated transforms

### Bundle Size
- Tree shaking eliminates unused code
- SVG icons for scalability
- Tailwind CSS purging removes unused styles
- Component code splitting

## 🧪 Testing

### Test Coverage Areas
- Component rendering with different data states
- Tab navigation and state management
- 3D flip card interactions
- Responsive behavior across breakpoints
- Error states and recovery
- Internationalization with all languages

### Mock Data Testing
```typescript
// Test with controlled recipe data
const mockRecipe: FinalRecipeProtocol = {
  recipe_name_localized: "Test Recipe",
  selected_oils: [...],
  // ... other properties
};
```

## 🚀 Deployment

### Production Checklist
- [ ] All TypeScript errors resolved
- [ ] Translation files complete for all languages
- [ ] Responsive design tested across devices
- [ ] Error handling verified
- [ ] Performance optimizations applied
- [ ] Accessibility compliance checked

### Environment Configuration
- API endpoints configurable via environment variables
- Feature flags for gradual rollout
- Monitoring and logging configured
- Error tracking integration (Sentry)

## 🔮 Future Enhancements

### Planned Features
- **Recipe Variations**: Multiple recipes per time slot
- **User Preferences**: Customizable oil preferences
- **Recipe History**: Save and revisit previous recipes
- **Social Features**: Share recipes with others
- **Advanced Analytics**: Usage patterns and optimization

### Extension Points
- Clean component interfaces for new features
- Modular architecture supports easy additions
- Internationalization system ready for new languages
- State management structure accommodates new data

---

## 📞 Support

## 🔧 Navigation Troubleshooting

### Common Issues with Continue Button

**Issue**: Continue button remains disabled after oil enrichment
**Solution**: Verify therapeutic properties enrichment status
```typescript
// Check in browser console:
therapeuticProperties.forEach(p =>
  console.log(p.property_name_localized, 'isEnriched:', p.isEnriched)
);
```

**Issue**: Navigation blocked with validation error
**Solution**: Ensure all prerequisite data is present
```typescript
// Required data checklist:
✅ healthConcern (step 1)
✅ demographics (step 2)
✅ selectedCauses.length > 0 (step 3)
✅ selectedSymptoms.length > 0 (step 4)
✅ therapeuticProperties.length > 0 (step 5)
✅ therapeuticProperties.some(p => p.isEnriched) (step 5)
```

### Expected Console Log Patterns

**Successful Navigation Flow**:
```
🚀 [Properties Navigation] Continue button clicked
🔍 [Properties Navigation] Navigation validation: { canGoNext: true }
🔍 [Store Navigation] Final Recipes validation: { result: true }
✅ [Properties Navigation] Validation passed, navigating to next step
🎯 [Navigation Hook] goToNext called: { nextStep: 'final-recipes' }
```

**Failed Navigation Flow**:
```
🚀 [Properties Navigation] Continue button clicked
🔍 [Store Navigation] Final Recipes validation: { result: false }
❌ [Properties Navigation] Cannot navigate - validation failed
```

### Authentication Loop Fix

**Issue**: Infinite re-render loop when Final Recipes component mounts
**Cause**: Unstable `useEffect` dependencies causing repeated auth hook calls
**Solution**: Use primitive values instead of object references in dependencies

```typescript
// Fixed dependency array (stable primitives)
useEffect(() => {
  // Auto-generation logic
}, [
  !!healthConcern,           // Boolean instead of object
  !!demographics,           // Boolean instead of object
  selectedCauses.length,    // Number instead of array
  selectedSymptoms.length,  // Number instead of array
  suggestedOils.length,     // Number instead of array
  finalRecipes.hasStartedGeneration
]);
```

### User Experience Flow

1. **Properties Step**: Auto-enrichment begins when component mounts
2. **Progress Indicators**: Loading states show enrichment progress
3. **Completion**: All properties show enriched oils with safety data
4. **Button State**: "Continue" button enables automatically
5. **Navigation**: Click Continue → `/dashboard/create-recipe/final-recipes`
6. **Final Recipes**: Component mounts without auth loops and auto-starts recipe generation

For technical questions or issues:
1. Check the comprehensive technical specification document
2. Review console logs for debugging information
3. Inspect Zustand store state for data issues
4. Verify translation keys are properly configured
5. Test with mock data to isolate issues

**Key Files for Troubleshooting:**
- `final-recipes-display.tsx` - Main component logic
- `recipe-store.ts` - State management and navigation validation
- `use-recipe-navigation.ts` - Navigation hook logic
- `properties-display.tsx` - Properties step navigation
- `use-create-recipe-streaming.ts` - AI integration
- `create-recipe.json` - Translation files
