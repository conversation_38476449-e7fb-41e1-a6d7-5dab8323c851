/**
 * @fileoverview Safety warnings component for essential oil recipes
 * Shows age-appropriate safety warnings and guidelines
 */

'use client';

import React from 'react';
import { DemographicsData, SafetyWarning } from '../types/recipe.types';
import { assessOilSafety, getRecommendedDilution } from '../utils/safety-filter';

interface SafetyWarningsProps {
  demographics: DemographicsData | null;
  customWarnings?: SafetyWarning[];
}

/**
 * Safety warnings component with age-appropriate guidelines
 */
export function SafetyWarnings({ demographics, customWarnings = [] }: SafetyWarningsProps) {
  if (!demographics) {
    return null;
  }

  const isChild = demographics.specificAge < 10;
  const isTeenager = demographics.specificAge >= 10 && demographics.specificAge < 18;
  const recommendedDilution = getRecommendedDilution(demographics.specificAge);

  // Generate age-specific warnings
  const ageWarnings: SafetyWarning[] = [];

  if (isChild) {
    ageWarnings.push({
      warning_type: 'age_restriction',
      severity: 'high',
      message_localized: 'Atenção: Criança menor de 10 anos',
      guidance_localized: 'Use apenas óleos seguros para crianças com diluição máxima de 0,5%. Evite óleos dermocáusticos como canela, cravo e orégano.'
    });
  }

  if (isTeenager) {
    ageWarnings.push({
      warning_type: 'dilution',
      severity: 'medium',
      message_localized: 'Diluição recomendada para adolescentes',
      guidance_localized: `Use diluição máxima de ${recommendedDilution}% para segurança adequada.`
    });
  }

  // Combine all warnings
  const allWarnings = [...ageWarnings, ...customWarnings];

  if (allWarnings.length === 0) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-2xl p-6">
        <div className="flex items-center gap-3 mb-3">
          <CheckIcon className="h-6 w-6 text-green-600" />
          <h3 className="text-lg font-semibold text-green-800">Perfil de Segurança</h3>
        </div>
        <p className="text-green-700">
          Baseado na sua idade e perfil, as receitas recomendadas são seguras quando usadas conforme as instruções.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* High severity warnings */}
      {allWarnings.filter(w => w.severity === 'high').map((warning, index) => (
        <WarningCard key={`high-${index}`} warning={warning} />
      ))}

      {/* Medium severity warnings */}
      {allWarnings.filter(w => w.severity === 'medium').map((warning, index) => (
        <WarningCard key={`medium-${index}`} warning={warning} />
      ))}

      {/* Low severity warnings */}
      {allWarnings.filter(w => w.severity === 'low').map((warning, index) => (
        <WarningCard key={`low-${index}`} warning={warning} />
      ))}

      {/* General safety guidelines */}
      <GeneralSafetyGuidelines isChild={isChild} />
    </div>
  );
}

/**
 * Individual warning card component
 */
interface WarningCardProps {
  warning: SafetyWarning;
}

function WarningCard({ warning }: WarningCardProps) {
  const severityConfig = {
    high: {
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      iconColor: 'text-red-600',
      textColor: 'text-red-800',
      icon: <AlertTriangleIcon />
    },
    medium: {
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
      iconColor: 'text-yellow-600',
      textColor: 'text-yellow-800',
      icon: <AlertCircleIcon />
    },
    low: {
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      iconColor: 'text-blue-600',
      textColor: 'text-blue-800',
      icon: <InfoIcon />
    }
  };

  const config = severityConfig[warning.severity];

  return (
    <div className={`${config.bgColor} ${config.borderColor} border rounded-2xl p-6`}>
      <div className="flex items-start gap-3">
        <span className={`${config.iconColor} flex-shrink-0 mt-0.5`}>
          {config.icon}
        </span>
        <div className="flex-1">
          <h4 className={`font-semibold ${config.textColor} mb-2`}>
            {warning.message_localized}
          </h4>
          <p className={`${config.textColor} text-sm leading-relaxed`}>
            {warning.guidance_localized}
          </p>
          {warning.warning_type && (
            <span className={`inline-block mt-2 px-2 py-1 rounded-full text-xs font-medium ${config.bgColor} ${config.textColor}`}>
              {getWarningTypeLabel(warning.warning_type)}
            </span>
          )}
        </div>
      </div>
    </div>
  );
}

/**
 * General safety guidelines component
 */
interface GeneralSafetyGuidelinesProps {
  isChild: boolean;
}

function GeneralSafetyGuidelines({ isChild }: GeneralSafetyGuidelinesProps) {
  return (
    <div className="bg-gray-50 border border-gray-200 rounded-2xl p-6">
      <div className="flex items-center gap-3 mb-4">
        <BookIcon className="h-6 w-6 text-gray-600" />
        <h3 className="text-lg font-semibold text-gray-800">
          Diretrizes Gerais de Segurança
        </h3>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h4 className="font-semibold text-gray-700 mb-2">Antes do Uso:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Faça um teste de sensibilidade em pequena área da pele</li>
            <li>• Verifique se não há alergias conhecidas aos ingredientes</li>
            <li>• Consulte um profissional se estiver grávida ou amamentando</li>
            {isChild && <li>• Supervisão adulta é obrigatória para crianças</li>}
          </ul>
        </div>
        
        <div>
          <h4 className="font-semibold text-gray-700 mb-2">Durante o Uso:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Evite contato com olhos e mucosas</li>
            <li>• Não aplique em feridas abertas ou pele irritada</li>
            <li>• Interrompa o uso se houver reação adversa</li>
            <li>• Mantenha hidratação adequada</li>
          </ul>
        </div>
        
        <div>
          <h4 className="font-semibold text-gray-700 mb-2">Armazenamento:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Mantenha em local fresco e seco</li>
            <li>• Proteja da luz solar direta</li>
            <li>• Use frascos de vidro escuro</li>
            <li>• Mantenha fora do alcance de crianças</li>
          </ul>
        </div>
        
        <div>
          <h4 className="font-semibold text-gray-700 mb-2">Emergência:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Em caso de irritação, lave com água abundante</li>
            <li>• Se ingerido acidentalmente, procure ajuda médica</li>
            <li>• Mantenha os números de emergência à mão</li>
            <li>• Documente qualquer reação adversa</li>
          </ul>
        </div>
      </div>
    </div>
  );
}

/**
 * Get warning type label in Portuguese
 */
function getWarningTypeLabel(warningType: string): string {
  const labels: Record<string, string> = {
    age_restriction: 'Restrição de Idade',
    pregnancy: 'Gravidez/Amamentação',
    phototoxicity: 'Fotossensibilidade',
    dilution: 'Diluição',
    general: 'Geral'
  };
  
  return labels[warningType] || 'Aviso';
}

// ============================================================================
// ICON COMPONENTS
// ============================================================================

function AlertTriangleIcon() {
  return (
    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
    </svg>
  );
}

function AlertCircleIcon() {
  return (
    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  );
}

function InfoIcon() {
  return (
    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  );
}

function CheckIcon({ className }: { className?: string }) {
  return (
    <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
    </svg>
  );
}

function BookIcon({ className }: { className?: string }) {
  return (
    <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
    </svg>
  );
}
