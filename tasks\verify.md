

The final step implementation was documented here:
- docs\create-recipe\readme\final-recipes-protocols-step\final-recipes-quick-reference.md
- docs\create-recipe\readme\final-recipes-protocols-step\final-recipes-technical-specification.md


- tasks\prd-create-recipe-final-step.md
- tasks\tasks-prd-create-recipe-final-step.md

The proper trigger to the final step should be the continue button on the properties step. I think the continue button for the step after the properties is located at: 
- src\features\create-recipe\components\recipe-navigation-buttons.tsx 

The data to be sent to the API from the properties step is located the overlay button Copy JSON (minimal), but that is not the trigger to the final step. It was a dev only feature only to see all the data gathered from all the previous steps.