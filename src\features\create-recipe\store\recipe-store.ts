/**
 * @fileoverview Zustand store for Essential Oil Recipe Creator wizard state management.
 * Implements non-persistent state for reset-on-refresh behavior.
 * Data is intentionally not persisted so browser refresh clears all state.
 */

import { create } from 'zustand';
import { RecipeStep } from '../types/recipe.types';
import type {
  RecipeWizardState,
  RecipeWizardActions,
  HealthConcernData,
  DemographicsData,
  PotentialCause,
  PotentialSymptom,
  TherapeuticProperty,
  PropertyOilSuggestions,
  EnrichedEssentialOil,
  FinalRecipeProtocol,
  FinalRecipeStatus,
  RecipeTimeSlot
} from '../types/recipe.types';

import {
  DEFAULT_STEP
} from '../constants/recipe.constants';

/**
 * Generates a UUID v4 string
 */
function generateUUID(): string {
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }
  // Fallback for environments without crypto.randomUUID
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Combined store interface with state and actions
 */
interface RecipeStore extends RecipeWizardState, RecipeWizardActions {}

/**
 * Initial state for the recipe wizard
 */
const initialState: Omit<RecipeWizardState, keyof RecipeWizardActions> = {
  // Step data
  healthConcern: null,
  demographics: null,
  selectedCauses: [],
  selectedSymptoms: [],
  therapeuticProperties: [],
  suggestedOils: [],
  finalRecipes: {
    morning: {
      recipe: null,
      status: { status: 'idle', retry_count: 0 }
    },
    midDay: {
      recipe: null,
      status: { status: 'idle', retry_count: 0 }
    },
    night: {
      recipe: null,
      status: { status: 'idle', retry_count: 0 }
    },
    isGenerating: false,
    hasStartedGeneration: false,
    globalError: null
  },
  
  // API response data
  potentialCauses: [],
  potentialSymptoms: [],
  
  // Navigation state
  currentStep: DEFAULT_STEP,
  completedSteps: [],
  
  // Loading and error states
  isLoading: false,
  error: null,

  // AI Streaming states
  isStreamingCauses: false,
  isStreamingSymptoms: false,
  isStreamingProperties: false,
  isStreamingOils: false,
  isStreamingFinalRecipes: false,
  streamingError: null,
  
  // Oil enrichment states
  propertyEnrichmentStatus: {},
  
  // Auto-analysis state
  shouldAutoAnalyzeProperties: false,
  
  // Metadata
  lastUpdated: new Date(),
  sessionId: generateUUID()
};



/**
 * Main recipe wizard store WITHOUT persistence for reset-on-refresh behavior
 * Data is intentionally not persisted so browser refresh clears all state
 */
export const useRecipeStore = create<RecipeStore>()((set, get) => ({
  ...initialState,
      
      // Step navigation actions - optimized to reduce unnecessary re-renders
      setCurrentStep: (step: RecipeStep) => {
        set((state) => {
          // Only update if step actually changed
          if (state.currentStep === step) return state;
          return {
            currentStep: step,
            lastUpdated: new Date()
          };
        });
      },

      markStepCompleted: (step: RecipeStep) => {
        set((state) => {
          // Only update if step isn't already completed
          if (state.completedSteps.includes(step)) return state;

          const completedSteps = [...state.completedSteps, step];
          return {
            completedSteps,
            lastUpdated: new Date()
          };
        });
      },
      
      canNavigateToStep: (step: RecipeStep): boolean => {
        const state = get();

        console.log(`🔍 [Store Navigation] Checking navigation to step: ${step}`);

        // Always allow navigation to health concern (first step)
        if (step === RecipeStep.HEALTH_CONCERN) {
          console.log('✅ [Store Navigation] Health concern step - always allowed');
          return true;
        }

        // Check if previous steps are completed
        switch (step) {
          case RecipeStep.DEMOGRAPHICS:
            const canNavigateToDemographics = !!state.healthConcern;
            console.log(`🔍 [Store Navigation] Demographics validation:`, {
              hasHealthConcern: !!state.healthConcern,
              result: canNavigateToDemographics
            });
            return canNavigateToDemographics;

          case RecipeStep.CAUSES:
            const canNavigateToCauses = !!state.healthConcern && !!state.demographics;
            console.log(`🔍 [Store Navigation] Causes validation:`, {
              hasHealthConcern: !!state.healthConcern,
              hasDemographics: !!state.demographics,
              result: canNavigateToCauses
            });
            return canNavigateToCauses;

          case RecipeStep.SYMPTOMS:
            const canNavigateToSymptoms = !!state.healthConcern && !!state.demographics && state.selectedCauses.length > 0;
            console.log(`🔍 [Store Navigation] Symptoms validation:`, {
              hasHealthConcern: !!state.healthConcern,
              hasDemographics: !!state.demographics,
              selectedCausesCount: state.selectedCauses.length,
              result: canNavigateToSymptoms
            });
            return canNavigateToSymptoms;

          case RecipeStep.PROPERTIES:
            const canNavigateToProperties = !!state.healthConcern && !!state.demographics &&
                   state.selectedCauses.length > 0 && state.selectedSymptoms.length > 0;
            console.log(`🔍 [Store Navigation] Properties validation:`, {
              hasHealthConcern: !!state.healthConcern,
              hasDemographics: !!state.demographics,
              selectedCausesCount: state.selectedCauses.length,
              selectedSymptomsCount: state.selectedSymptoms.length,
              result: canNavigateToProperties
            });
            return canNavigateToProperties;

          case RecipeStep.FINAL_RECIPES:
            // Can navigate to Final Recipes when all previous steps are completed
            // and therapeutic properties have been enriched with oils
            const hasBasicData = !!state.healthConcern && !!state.demographics &&
                   state.selectedCauses.length > 0 && state.selectedSymptoms.length > 0;
            const hasProperties = state.therapeuticProperties.length > 0;
            const hasEnrichedProperties = state.therapeuticProperties.some(p => p.isEnriched);
            const canNavigateToFinalRecipes = hasBasicData && hasProperties && hasEnrichedProperties;

            console.log(`🔍 [Store Navigation] Final Recipes validation:`, {
              hasHealthConcern: !!state.healthConcern,
              hasDemographics: !!state.demographics,
              selectedCausesCount: state.selectedCauses.length,
              selectedSymptomsCount: state.selectedSymptoms.length,
              therapeuticPropertiesCount: state.therapeuticProperties.length,
              enrichedPropertiesCount: state.therapeuticProperties.filter(p => p.isEnriched).length,
              hasBasicData,
              hasProperties,
              hasEnrichedProperties,
              result: canNavigateToFinalRecipes
            });

            // Log detailed property enrichment status
            state.therapeuticProperties.forEach((prop, index) => {
              console.log(`🔍 [Store Navigation] Property ${index + 1}: ${prop.property_name_localized}`, {
                isEnriched: prop.isEnriched,
                oilsCount: prop.suggested_oils?.length || 0,
                enrichedOilsCount: prop.suggested_oils?.filter(oil => oil.enrichment_status).length || 0
              });
            });

            return canNavigateToFinalRecipes;

          default:
            console.log(`❌ [Store Navigation] Unknown step: ${step}`);
            return false;
        }
      },
      
      // Data update actions
      updateHealthConcern: (data: HealthConcernData) => {
        set(() => ({
          healthConcern: data,
          lastUpdated: new Date()
        }));
      },
      
      updateDemographics: (data: DemographicsData) => {
        set(() => ({
          demographics: data,
          lastUpdated: new Date()
        }));
      },
      
      updateSelectedCauses: (causes: PotentialCause[]) => {
        set(() => ({
          selectedCauses: causes,
          // Clear dependent data when causes change
          selectedSymptoms: [],
          therapeuticProperties: [],
          suggestedOils: [],
          potentialSymptoms: [],
          lastUpdated: new Date()
        }));
      },
      
      updateSelectedSymptoms: (symptoms: PotentialSymptom[]) => {
        set(() => ({
          selectedSymptoms: symptoms,
          // Clear dependent data when symptoms change
          therapeuticProperties: [],
          suggestedOils: [],
          lastUpdated: new Date()
        }));
      },
      
      updateTherapeuticProperties: (properties: TherapeuticProperty[], source: string) => {
        const currentState = get().therapeuticProperties;
        console.log('ACTION: updateTherapeuticProperties');
        console.log('PAYLOAD:', { properties, source });

        // Only keep canonical fields for each property
        const canonicalProperties = properties.map(newProp => {
          const existing = currentState.find(cp => cp.property_id === newProp.property_id);
          // If existing is enriched and has oils, keep it
          if (existing && existing.isEnriched && existing.suggested_oils && existing.suggested_oils.length > 0) {
            return existing;
          }
          // Otherwise, only keep canonical fields
          return {
            property_id: newProp.property_id,
            property_name_localized: newProp.property_name_localized,
            property_name_english: newProp.property_name_english,
            description_contextual_localized: newProp.description_contextual_localized,
            addresses_cause_ids: newProp.addresses_cause_ids || [],
            addresses_symptom_ids: newProp.addresses_symptom_ids || [],
            relevancy_score: newProp.relevancy_score,
            suggested_oils: newProp.suggested_oils || [],
            isLoadingOils: newProp.isLoadingOils ?? false,
            errorLoadingOils: newProp.errorLoadingOils ?? null,
            isEnriched: newProp.isEnriched ?? false,
          };
        });

        set(() => ({
          therapeuticProperties: canonicalProperties,
          // Clear dependent data when properties change
          suggestedOils: [],
          lastUpdated: new Date()
        }));
      },
      
      updateSuggestedOils: (oils: PropertyOilSuggestions[]) => {
        set(() => ({
          suggestedOils: oils,
          lastUpdated: new Date()
        }));
      },

      updateFinalRecipes: (timeSlot: RecipeTimeSlot, recipe: FinalRecipeProtocol) => {
        set((state) => ({
          finalRecipes: {
            ...state.finalRecipes,
            [timeSlot === 'mid-day' ? 'midDay' : timeSlot]: {
              recipe,
              status: { status: 'success' as const, retry_count: 0 }
            }
          },
          lastUpdated: new Date()
        }));
      },
      
      // API data update actions
      setPotentialCauses: (causes: PotentialCause[]) => {
        set(() => ({
          potentialCauses: causes,
          lastUpdated: new Date()
        }));
      },
      
      setPotentialSymptoms: (symptoms: PotentialSymptom[]) => {
        set(() => ({
          potentialSymptoms: symptoms,
          lastUpdated: new Date()
        }));
      },
      
      // State management actions - optimized to prevent unnecessary re-renders
      setLoading: (loading: boolean) => {
        set((state) => {
          // Only update if loading state actually changed
          if (state.isLoading === loading) return state;
          return {
            isLoading: loading,
            lastUpdated: new Date()
          };
        });
      },

      setError: (error: string | null) => {
        set((state) => {
          // Only update if error actually changed
          if (state.error === error && !state.isLoading) return state;
          return {
            error,
            isLoading: false, // Clear loading when setting error
            lastUpdated: new Date()
          };
        });
      },

      clearError: () => {
        set((state) => {
          // Only update if there's actually an error to clear
          if (!state.error) return state;
          return {
            error: null,
            lastUpdated: new Date()
          };
        });
      },

      // AI Streaming state management actions - optimized to reduce re-renders
      setStreamingCauses: (isStreaming: boolean) => {
        set((state) => {
          // Only update if streaming state actually changed
          if (state.isStreamingCauses === isStreaming) return state;
          return {
            isStreamingCauses: isStreaming,
            streamingError: isStreaming ? null : state.streamingError, // Clear error when starting new stream
            lastUpdated: new Date()
          };
        });
      },

      setStreamingSymptoms: (isStreaming: boolean) => {
        set((state) => {
          // Only update if streaming state actually changed
          if (state.isStreamingSymptoms === isStreaming) return state;
          return {
            isStreamingSymptoms: isStreaming,
            streamingError: isStreaming ? null : state.streamingError, // Clear error when starting new stream
            lastUpdated: new Date()
          };
        });
      },

      setStreamingProperties: (isStreaming: boolean) => {
        set((state) => {
          // Only update if streaming state actually changed
          if (state.isStreamingProperties === isStreaming) return state;
          return {
            isStreamingProperties: isStreaming,
            streamingError: isStreaming ? null : state.streamingError, // Clear error when starting new stream
            lastUpdated: new Date()
          };
        });
      },

      setStreamingOils: (isStreaming: boolean) => {
        set((state) => {
          // Only update if streaming state actually changed
          if (state.isStreamingOils === isStreaming) return state;
          return {
            isStreamingOils: isStreaming,
            streamingError: isStreaming ? null : state.streamingError, // Clear error when starting new stream
            lastUpdated: new Date()
          };
        });
      },

      setStreamingFinalRecipes: (isStreaming: boolean) => {
        set((state) => {
          // Only update if streaming state actually changed
          if (state.isStreamingFinalRecipes === isStreaming) return state;
          return {
            isStreamingFinalRecipes: isStreaming,
            streamingError: isStreaming ? null : state.streamingError, // Clear error when starting new stream
            lastUpdated: new Date()
          };
        });
      },

      setStreamingError: (error: string | null) => {
        set(() => ({
          streamingError: error,
          isStreamingCauses: false, // Stop streaming on error
          isStreamingSymptoms: false, // Stop streaming on error
          isStreamingProperties: false, // Stop streaming on error
          isStreamingOils: false, // Stop streaming on error
          isStreamingFinalRecipes: false, // Stop streaming on error
          lastUpdated: new Date()
        }));
      },

      clearStreamingError: () => {
        set(() => ({
          streamingError: null,
          lastUpdated: new Date()
        }));
      },

      // Batched update actions to minimize re-renders
      batchUpdateStreamingState: (updates: {
        isStreamingCauses?: boolean;
        isStreamingSymptoms?: boolean;
        isStreamingProperties?: boolean;
        isStreamingOils?: boolean;
        streamingError?: string | null;
      }) => {
        set((state) => {
          const newState = { ...state };
          let hasChanges = false;

          // Only update fields that have actually changed
          if (updates.isStreamingCauses !== undefined && state.isStreamingCauses !== updates.isStreamingCauses) {
            newState.isStreamingCauses = updates.isStreamingCauses;
            hasChanges = true;
          }
          if (updates.isStreamingSymptoms !== undefined && state.isStreamingSymptoms !== updates.isStreamingSymptoms) {
            newState.isStreamingSymptoms = updates.isStreamingSymptoms;
            hasChanges = true;
          }
          if (updates.isStreamingProperties !== undefined && state.isStreamingProperties !== updates.isStreamingProperties) {
            newState.isStreamingProperties = updates.isStreamingProperties;
            hasChanges = true;
          }
          if (updates.isStreamingOils !== undefined && state.isStreamingOils !== updates.isStreamingOils) {
            newState.isStreamingOils = updates.isStreamingOils;
            hasChanges = true;
          }
          if (updates.streamingError !== undefined && state.streamingError !== updates.streamingError) {
            newState.streamingError = updates.streamingError;
            hasChanges = true;
          }

          // Only update lastUpdated if there were actual changes
          if (hasChanges) {
            newState.lastUpdated = new Date();
            return newState;
          }

          return state; // No changes, return existing state
        });
      },

      batchUpdateStepData: (updates: {
        currentStep?: RecipeStep;
        completedSteps?: RecipeStep[];
        healthConcern?: HealthConcernData | null;
        demographics?: DemographicsData | null;
        selectedCauses?: PotentialCause[];
        selectedSymptoms?: PotentialSymptom[];
        therapeuticProperties?: TherapeuticProperty[];
        suggestedOils?: PropertyOilSuggestions[];
        potentialCauses?: PotentialCause[];
        potentialSymptoms?: PotentialSymptom[];
      }) => {
        set((state) => {
          const newState = { ...state };
          let hasChanges = false;

          // Only update fields that have actually changed
          Object.entries(updates).forEach(([key, value]) => {
            if (value !== undefined && JSON.stringify(state[key as keyof RecipeWizardState]) !== JSON.stringify(value)) {
              (newState as any)[key] = value;
              hasChanges = true;
            }
          });

          // Only update lastUpdated if there were actual changes
          if (hasChanges) {
            newState.lastUpdated = new Date();
            return newState;
          }

          return state; // No changes, return existing state
        });
      },

      batchUpdateLoadingAndError: (updates: {
        isLoading?: boolean;
        error?: string | null;
      }) => {
        set((state) => {
          const newState = { ...state };
          let hasChanges = false;

          if (updates.isLoading !== undefined && state.isLoading !== updates.isLoading) {
            newState.isLoading = updates.isLoading;
            hasChanges = true;
          }
          if (updates.error !== undefined && state.error !== updates.error) {
            newState.error = updates.error;
            hasChanges = true;
          }

          // Only update lastUpdated if there were actual changes
          if (hasChanges) {
            newState.lastUpdated = new Date();
            return newState;
          }

          return state; // No changes, return existing state
        });
      },
      
      /**
       * Clears data for all steps after the specified step
       * Used when navigating backwards to ensure data consistency
       */
      clearStepsAfter: (currentStep: RecipeStep) => {
        console.log(`🧹 Clearing steps after: ${currentStep}`);

        set((state) => {
          const updates: Partial<RecipeWizardState> = {
            lastUpdated: new Date()
          };

          // Clear completed steps that come after the current step
          const stepOrder = [
            RecipeStep.HEALTH_CONCERN,
            RecipeStep.DEMOGRAPHICS,
            RecipeStep.CAUSES,
            RecipeStep.SYMPTOMS,
            RecipeStep.PROPERTIES,
            RecipeStep.FINAL_RECIPES
            // Note: OILS step removed - oils are now nested within PROPERTIES
          ];

          const currentStepIndex = stepOrder.indexOf(currentStep);
          const stepsToRemove = stepOrder.slice(currentStepIndex + 1);

          updates.completedSteps = state.completedSteps.filter(
            step => !stepsToRemove.includes(step)
          );

          // Clear data based on which step we're going back to
          switch (currentStep) {
            case RecipeStep.HEALTH_CONCERN:
              // Clear everything except health concern
              updates.demographics = null;
              updates.selectedCauses = [];
              updates.selectedSymptoms = [];
              updates.therapeuticProperties = [];
              updates.suggestedOils = [];
              updates.potentialCauses = [];
              updates.potentialSymptoms = [];
              break;

            case RecipeStep.DEMOGRAPHICS:
              // Clear causes and everything after
              updates.selectedCauses = [];
              updates.selectedSymptoms = [];
              updates.therapeuticProperties = [];
              updates.suggestedOils = [];
              updates.potentialCauses = [];
              updates.potentialSymptoms = [];
              break;

            case RecipeStep.CAUSES:
              // Clear symptoms and everything after
              updates.selectedSymptoms = [];
              updates.therapeuticProperties = [];
              updates.suggestedOils = [];
              updates.potentialSymptoms = [];
              break;

            case RecipeStep.SYMPTOMS:
              // Clear properties and oils
              updates.therapeuticProperties = [];
              updates.suggestedOils = [];
              break;

            case RecipeStep.PROPERTIES:
              // This is now the final step - nothing to clear after it
              // (oils are nested within properties, not a separate step)
              break;
          }

          console.log(`✅ Cleared data for steps after ${currentStep}:`, {
            clearedSteps: stepsToRemove,
            remainingCompletedSteps: updates.completedSteps
          });

          return { ...state, ...updates };
        });
      },

      /**
       * Clears specific step data
       * Used for targeted data clearing
       */
      clearStepData: (step: RecipeStep) => {
        console.log(`🧹 Clearing data for step: ${step}`);

        const stateToClear: Partial<RecipeWizardState> = {};
          switch (step) {
            case RecipeStep.HEALTH_CONCERN:
            stateToClear.healthConcern = null;
              break;
            case RecipeStep.DEMOGRAPHICS:
            stateToClear.demographics = null;
              break;
            case RecipeStep.CAUSES:
            stateToClear.selectedCauses = [];
            stateToClear.potentialCauses = [];
              break;
            case RecipeStep.SYMPTOMS:
            stateToClear.selectedSymptoms = [];
            stateToClear.potentialSymptoms = [];
              break;
            case RecipeStep.PROPERTIES:
            stateToClear.therapeuticProperties = [];
            stateToClear.suggestedOils = [];
              break;
          }
        set(stateToClear);
      },

      resetWizard: () => {
        console.log('🔄 Starting recipe wizard reset...');

        // Reset to initial state with new session ID and explicitly clear all states
        set(() => ({
          ...initialState,
          error: null, // Explicitly clear any error state
          isLoading: false, // Explicitly clear loading state
          isStreamingCauses: false, // Clear streaming states
          isStreamingSymptoms: false,
          isStreamingProperties: false,
          isStreamingOils: false,
          streamingError: null,
          sessionId: generateUUID(), // Generate new session ID
          lastUpdated: new Date()
        }));

        console.log('✅ Recipe wizard reset completed - all data and states cleared');
      },

      // Oil enrichment state management
      setPropertyEnrichmentStatus: (propertyId: string, status: 'idle' | 'loading' | 'success' | 'error') => {
        set((state) => ({
          propertyEnrichmentStatus: {
            ...state.propertyEnrichmentStatus,
            [propertyId]: status,
          },
          lastUpdated: new Date()
        }));
      },

      updatePropertyWithEnrichedOils: (propertyId: string, enrichedOils: EnrichedEssentialOil[]) => {
        set((state) => {
          console.log('ACTION: updatePropertyWithEnrichedOils');
          console.log('PAYLOAD:', { propertyId, enrichedOils });
          console.log('PRE-UPDATE STATE:', state.therapeuticProperties);

          const updatedProperties = state.therapeuticProperties.map(p => {
            if (p.property_id === propertyId) {
              // Consider an oil enriched if it has been processed (has an enrichment_status)
              const updatedOils = enrichedOils.map(oil => ({
                ...oil,
                isEnriched: !!oil.enrichment_status // Oil is enriched if it has been processed
              }));

              // Property is enriched if all oils have been processed
              const allOilsProcessed = updatedOils.every(oil => !!oil.enrichment_status);

              return {
                ...p,
                suggested_oils: updatedOils,
                isEnriched: allOilsProcessed
              };
            }
            return p;
          });

          // Log enrichment statistics
          console.log(`✅ [recipe-store] Updated property ${propertyId} with ${enrichedOils.length} oils:`, {
            enriched: enrichedOils.filter(o => o.enrichment_status === 'enriched').length,
            not_found: enrichedOils.filter(o => o.enrichment_status === 'not_found').length,
            discarded: enrichedOils.filter(o => o.enrichment_status === 'discarded').length
          });
          
          console.log('POST-UPDATE STATE:', updatedProperties);
          return {
            therapeuticProperties: updatedProperties,
            lastUpdated: new Date()
          };
        });
      },

      // Auto-analysis state management
      /**
       * Sets the flag to trigger automatic oil analysis when navigating to properties page
       * @param should - Boolean flag indicating whether auto-analysis should be triggered
       * 
       * This flag is set to true when users navigate from symptoms to properties page,
       * and is automatically cleared after the auto-analysis is triggered to prevent
       * duplicate API calls.
       */
      setShouldAutoAnalyzeProperties: (should: boolean) => {
        set((state) => {
          // Only update if the flag actually changed
          if (state.shouldAutoAnalyzeProperties === should) return state;
          return {
            shouldAutoAnalyzeProperties: should,
            lastUpdated: new Date()
          };
        });
      },

      // Final recipes state management
      setFinalRecipeStatus: (timeSlot: RecipeTimeSlot, status: FinalRecipeStatus) => {
        set((state) => ({
          finalRecipes: {
            ...state.finalRecipes,
            [timeSlot === 'mid-day' ? 'midDay' : timeSlot]: {
              ...state.finalRecipes[timeSlot === 'mid-day' ? 'midDay' : timeSlot],
              status
            }
          },
          lastUpdated: new Date()
        }));
      },

      setFinalRecipesGenerating: (isGenerating: boolean) => {
        set((state) => ({
          finalRecipes: {
            ...state.finalRecipes,
            isGenerating,
            hasStartedGeneration: isGenerating || state.finalRecipes.hasStartedGeneration
          },
          lastUpdated: new Date()
        }));
      },

      setFinalRecipesGlobalError: (error: string | null) => {
        set((state) => ({
          finalRecipes: {
            ...state.finalRecipes,
            globalError: error
          },
          lastUpdated: new Date()
        }));
      },

      resetFinalRecipes: () => {
        set((state) => ({
          finalRecipes: {
            morning: {
              recipe: null,
              status: { status: 'idle', retry_count: 0 }
            },
            midDay: {
              recipe: null,
              status: { status: 'idle', retry_count: 0 }
            },
            night: {
              recipe: null,
              status: { status: 'idle', retry_count: 0 }
            },
            isGenerating: false,
            hasStartedGeneration: false,
            globalError: null
          },
          lastUpdated: new Date()
        }));
      }
    }));

/**
 * Selector hooks for specific parts of the state
 */
export const useRecipeNavigationStore = () => useRecipeStore((state) => ({
  currentStep: state.currentStep,
  completedSteps: state.completedSteps,
  setCurrentStep: state.setCurrentStep,
  markStepCompleted: state.markStepCompleted,
  canNavigateToStep: state.canNavigateToStep
}));

export const useRecipeData = () => useRecipeStore((state) => ({
  healthConcern: state.healthConcern,
  demographics: state.demographics,
  selectedCauses: state.selectedCauses,
  selectedSymptoms: state.selectedSymptoms,
  therapeuticProperties: state.therapeuticProperties,
  suggestedOils: state.suggestedOils
}));

export const useRecipeApiData = () => useRecipeStore((state) => ({
  potentialCauses: state.potentialCauses,
  potentialSymptoms: state.potentialSymptoms,
  setPotentialCauses: state.setPotentialCauses,
  setPotentialSymptoms: state.setPotentialSymptoms
}));

export const useRecipeStatus = () => useRecipeStore((state) => ({
  isLoading: state.isLoading,
  error: state.error,
  setLoading: state.setLoading,
  setError: state.setError,
  clearError: state.clearError
}));

export const useRecipeStreaming = () => useRecipeStore((state) => ({
  isStreamingCauses: state.isStreamingCauses,
  isStreamingSymptoms: state.isStreamingSymptoms,
  isStreamingProperties: state.isStreamingProperties,
  isStreamingOils: state.isStreamingOils,
  streamingError: state.streamingError,
  setStreamingCauses: state.setStreamingCauses,
  setStreamingSymptoms: state.setStreamingSymptoms,
  setStreamingProperties: state.setStreamingProperties,
  setStreamingOils: state.setStreamingOils,
  setStreamingError: state.setStreamingError,
  clearStreamingError: state.clearStreamingError
}));

/**
 * Utility function to clear all recipe data
 * Since we removed persistence, this just resets the store
 */
export const clearRecipeData = () => {
  try {
    useRecipeStore.getState().resetWizard();
  } catch (error) {
    console.error('Error clearing recipe data:', error);
  }
};
