// src/app/api/ai/streaming/route.ts
// Generic streaming API route using OpenAI Agents JS SDK patterns

import { NextRequest, NextResponse } from 'next/server';
import * as fs from 'fs/promises';
import { join } from 'path';
import {
  Agent,
  run,
  user,
  withTrace,
  setDefaultOpenAIKey,
  setOpenAIAPI
} from '@openai/agents';
import { parse } from 'best-effort-json-parser';
import { getPromptManager, PromptManagerError } from '@/lib/ai/utils/prompt-manager';
import {
  STREAMING_DATA_TYPES,
  isItemComplete,
  cleanItemData,
  getPrimaryDisplayField
} from '@/lib/ai/types/streaming-data-types';
import { suggestedOilsSearchTools } from '@/lib/ai/tools/suggested-oils-search-tool';
import { FileLogger } from '@/lib/debug/file-logger';
import { validateOpenAIKey, parseJsonBody, createErrorResponse, createSuccessResponse } from '@/lib/ai/utils/api-helpers';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

// Configure OpenAI Agents SDK
if (process.env['OPENAI_API_KEY']) {
  setDefaultOpenAIKey(process.env['OPENAI_API_KEY']);
  setOpenAIAPI('responses'); // Use Responses API for structured outputs
}

// API timeout for AI requests (30 seconds)
const API_TIMEOUT = 30000;

// API configuration

/**
 * Simple request validation for testing
 */
function validateStreamingRequest(data: any): { isValid: boolean; errors?: string[] } {
  const errors: string[] = [];

  if (!data.feature || typeof data.feature !== 'string') {
    errors.push('feature is required and must be a string');
  }

  if (!data.step || typeof data.step !== 'string') {
    errors.push('step is required and must be a string');
  }

  if (!data.data || typeof data.data !== 'object') {
    errors.push('data is required and must be an object');
  }

  return {
    isValid: errors.length === 0,
    errors: errors.length > 0 ? errors : undefined
  };
}



/**
 * Prepare template variables based on feature and step
 */
function prepareTemplateVariables(feature: string, data: any): Record<string, any> {


  // For create-recipe feature - properly structure template variables
  if (feature === 'create-recipe') {
    const demographics = data.demographics || {};
    const step = data.step || '';

    // Base template variables
    const templateVars: Record<string, any> = {
      // Health concern
      health_concern: data.health_concern || '',

      // Demographics - flatten for easier template access
      gender: demographics.gender || '',
      ageCategory: demographics.age_category || '',
      age_category: demographics.age_category || '',
      specificAge: demographics.age_specific || '',
      age_specific: demographics.age_specific || '',
      language: data.user_language || 'PT_BR',
      user_language: data.user_language || 'PT_BR',

      // Nested demographics object for complex templates
      demographics: demographics,

      // Selected data
      selected_causes: data.selected_causes || [],
      selected_symptoms: data.selected_symptoms || [],
      therapeutic_property: data.therapeutic_property || {},

      // Oil enrichment data (only the array, not individual property fields)
      suggested_oils: data.suggested_oils || []
    };

    // Debug logging for template variable population
    console.log(`[Template Variables] create-recipe feature (step: ${step}):`, {
      inputData: data,
      outputVariables: Object.keys(templateVars).reduce((acc, key) => {
        // Don't log large arrays in full
        if (Array.isArray(templateVars[key])) {
          acc[key] = `Array[${templateVars[key].length}]`;
        } else {
          acc[key] = templateVars[key];
        }
        return acc;
      }, {} as Record<string, any>)
    });

    return templateVars;
  }

  // Default: return data as-is for other features
  return data;
}

/**
 * Handle structured-only streaming (no text chunks, only structured data)
 * Uses best-effort-json-parser for bulletproof progressive parsing
 */
/**
 * Handle structured-only streaming (no text chunks, only structured data)
 * Uses best-effort-json-parser for bulletproof progressive parsing
 */
async function handleStructuredOnlyStreaming(
  result: any,
  controller: ReadableStreamDefaultController,
  encoder: TextEncoder,
  logger?: FileLogger
): Promise<void> {
  console.log('[Structured-Only Streaming] Starting buffer-based structured processing');

  let buffer = '';
  let totalChunksProcessed = 0;
  let totalItemsSent = 0;
  let finalData: any = null;

  // Track sent items and counts per data type to prevent duplicates
  const sentItems = new Set<string>();
  const lastSentCounts = new Map<string, number>();

  // Use imported configuration for all supported data types

  /**
   * Generic helper to send complete items for any data type
   */
  const sendCompleteItems = (parsedData: any) => {
    if (!parsedData?.data || typeof parsedData.data !== 'object') {
      return;
    }

    // Dynamically detect and process all supported data types
    for (const [dataType, config] of Object.entries(STREAMING_DATA_TYPES)) {
      let items: any[] = [];

      // Handle all data types using the standard structure (suggested_oils now directly under data)
      {
        // Handle standard data types
        const data = parsedData.data[dataType];
        if (!data) {
          continue; // Skip if this data type is not present
        }
        items = Array.isArray(data) ? data : [data];
      }

      if (items.length === 0) {
        continue; // Skip if no items found
      }

      const lastSentCount = lastSentCounts.get(dataType) || 0;

      for (let i = lastSentCount; i < items.length; i++) {
        const item = items[i];

        if (isItemComplete(item, config)) {
          // Use nested value access for ID field (handles paths like 'therapeutic_property_context.property_id')
          const getNestedValue = (obj: any, path: string): any => {
            return path.split('.').reduce((current, key) => {
              return current && current[key] !== undefined ? current[key] : undefined;
            }, obj);
          };

          const itemId = getNestedValue(item, config.idField) || 'unknown';
          const itemKey = `${dataType}-${i}-${itemId}`;

          if (!sentItems.has(itemKey)) {
            const cleanData = cleanItemData(item, config);

            const sseEvent = `data: ${JSON.stringify({
              type: 'structured_data',
              field: dataType,
              index: i,
              data: cleanData,
              timestamp: new Date().toISOString()
            })}

`;

            controller.enqueue(encoder.encode(sseEvent));
            sentItems.add(itemKey);
            totalItemsSent++;
            lastSentCounts.set(dataType, i + 1);

            const primaryField = getPrimaryDisplayField(dataType);
            console.log(`[Structured-Only Streaming] ✅ Sent complete ${config.displayName}:`, {
              index: i,
              name: cleanData[primaryField] || 'Unknown',
              totalSent: totalItemsSent,
              dataType
            });
          }
        }
      }
    }
  };

  // Process buffer using best-effort-json-parser (like reference code)
  const processBuffer = () => {
    try {
      // Use best-effort-json-parser to parse the buffer
      const parsed = parse(buffer);
      if (parsed) {
        sendCompleteItems(parsed);
      }
    } catch (error) {
      // Ignore parse errors - we'll try again with more data
    }
  };

  // Log buffer content for debugging
  const logBufferContent = (chunkCount: number) => {
    if (logger) {
      logger.writeLog(`Buffer content at chunk ${chunkCount} (${buffer.length} chars):`);
      logger.writeRawData({
        type: 'bufferSnapshot',
        chunkCount,
        bufferLength: buffer.length,
        bufferPreview: buffer.substring(0, 500),
        bufferSuffix: buffer.length > 500 ? buffer.substring(buffer.length - 100) : null
      });
    }
  };

  try {
    // Use toTextStream() to get character-by-character updates (like reference code)
    const textStream = result.toTextStream();

    for await (const textChunk of textStream) {
      buffer += textChunk;
      totalChunksProcessed++;

      // Process buffer every 50 chunks to reduce frequency (like reference code approach)
      if (totalChunksProcessed % 50 === 0) {
        processBuffer();
      }

      // Log progress every 200 chunks to avoid spam
      if (totalChunksProcessed % 200 === 0) {
        console.log('[Structured-Only Streaming] Progress:', {
          bufferLength: buffer.length,
          chunksProcessed: totalChunksProcessed,
          itemsSent: totalItemsSent
        });

        // Log buffer content for debugging
        logBufferContent(totalChunksProcessed);
      }
    }

    // Final processing to catch any remaining complete items
    processBuffer();

    console.log('[Structured-Only Streaming] Text streaming completed, waiting for final result');

    // Log final buffer content for debugging
    if (logger) {
      logger.writeLog(`Final buffer content (${buffer.length} chars):`);
      logger.writeRawData({
        type: 'finalBuffer',
        bufferLength: buffer.length,
        fullBuffer: buffer
      });
    }

    // Wait for completion and send final structured data
    await result.completed;
    finalData = result.finalOutput;

    if (finalData && typeof finalData === 'object') {
      // Final processing to ensure we have all complete items
      try {
        sendCompleteItems(finalData);

        const completionEvent = `data: ${JSON.stringify({
          type: 'structured_complete',
          data: finalData,
          stats: {
            totalChunksProcessed,
            totalItemsSent,
            finalBufferLength: buffer.length,
            itemsProcessed: sentItems.size
          },
          timestamp: new Date().toISOString()
        })}

`;

        controller.enqueue(encoder.encode(completionEvent));
        console.log('[Structured-Only Streaming] ✅ Final completion sent:', {
          totalChunksProcessed,
          totalItemsSent,
          itemsProcessed: sentItems.size,
          finalOutputKeys: finalData ? Object.keys(finalData) : []
        });
      } catch (finalError) {
        console.error('[Structured-Only Streaming] ❌ Error sending final data:', finalError);
        // Don't throw here - we want to complete gracefully
      }
    }

    // Log agent result AFTER streaming completes (not before)
    if (logger) {
      logger.logAgentResult(result);
    }
  } catch (error) {
    console.error('[Structured-Only Streaming] ❌ Error processing final output:', error);
    // Don't try to send error events here - the controller might be closed
    // The error will be handled by the outer try-catch in the stream handler
  }
}

// Note: Manual extraction function removed since AI agent now provides complete structured output

/**
 * Handle traditional text streaming
 */
async function handleTextStreaming(
  result: any,
  controller: ReadableStreamDefaultController,
  encoder: TextEncoder
): Promise<void> {
  console.log('[Text Streaming] Starting text stream processing');

  // Use toTextStream() as shown in the OpenAI Agents SDK examples
  const textStream = result.toTextStream();

  for await (const textChunk of textStream) {
    console.log('[Text Streaming] Text chunk received:', textChunk.length, 'chars');

    // Send text chunk as SSE
    const sseEvent = `data: ${JSON.stringify({
      type: 'text_chunk',
      content: textChunk
    })}

`;
    controller.enqueue(encoder.encode(sseEvent));
  }

  // Wait for completion and get final result
  console.log('[Text Streaming] Waiting for completion');
  await result.completed;

  // Send completion event with final data
  const completionEvent = `data: ${JSON.stringify({
    type: 'completion',
    final_data: result.finalOutput || []
  })}

`;
  controller.enqueue(encoder.encode(completionEvent));
}

// Note: Progressive data extraction function removed since AI agent now provides complete structured output

export async function POST(request: NextRequest): Promise<NextResponse> {
  const requestStartTime = Date.now();
  const traceId = `streaming-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  
  // Extract step name from request body for logging
  let stepName = 'unknown-step';
  try {
    const requestClone = request.clone();
    const body = await requestClone.json();
    stepName = (body.step || 'unknown').replace(/[^a-z0-9-]/gi, '_').toLowerCase();
  } catch (e) {
    console.warn('Could not extract step name from request');
  }

  // Log the request (fire and forget)
  const inputLogger = new FileLogger({ logDirectory: 'input', fileName: `${stepName}-${traceId}.json`, mode: 'transactional' });
  const requestBody = await request.clone().json();
  await inputLogger.log(requestBody);


  // Initialize debug logger for this session
  const logger = new FileLogger({ logDirectory: 'output', fileName: traceId, mode: 'streaming' });

  // Initialize specific debug loggers for final-recipes step
  let finalRecipesInputLogger: FileLogger | null = null;
  let finalRecipesOutputLogger: FileLogger | null = null;

  try {
    console.log('[Streaming API] Request started', { traceId });
    logger.writeLog(`Request started with traceId: ${traceId}`);

    // Validate API key using helper
    validateOpenAIKey();
    console.log('[Streaming API] API key validated');

    // Parse and validate request using helper
    const requestData = await parseJsonBody(request);
    console.log('[Streaming API] Request data parsed', { feature: requestData.feature, step: requestData.step });

    // Check for streaming mode override
    const streamingMode = requestData.streamingMode || 'auto'; // 'auto', 'text', 'structured'
    console.log('[Streaming API] Streaming mode:', streamingMode);

    const validation = validateStreamingRequest(requestData);

    if (!validation.isValid) {
      console.log('[Streaming API] Request validation failed', validation.errors);
      return createErrorResponse(
        `Invalid request data: ${validation.errors?.join(', ')}`,
        400
      );
    }

    console.log('[Streaming API] Request validation passed');
    const { feature, step, data } = requestData;

    // Initialize specific debug loggers for final-recipes step
    if (step === 'final-recipes') {
      const timeSlot = (data as any)?.timeSlot || 'unknown';
      const timestamp = Date.now();

      finalRecipesInputLogger = new FileLogger({
        logDirectory: 'input',
        fileName: `final-recipes-${timeSlot}-${timestamp}-input.json`,
        mode: 'transactional'
      });

      finalRecipesOutputLogger = new FileLogger({
        logDirectory: 'output',
        fileName: `final-recipes-${timeSlot}-${timestamp}-output.json`,
        mode: 'transactional'
      });

      console.log(`🍃 [Final Recipes Debug] Initialized debug loggers for ${timeSlot} time slot`);

      // Log comprehensive input data
      const inputData = {
        metadata: {
          step: 'final-recipes',
          timeSlot,
          timestamp: new Date().toISOString(),
          traceId
        },
        requestData: {
          feature,
          step,
          data
        },
        templateVariables: prepareTemplateVariables(feature, data)
      };

      await finalRecipesInputLogger.log(inputData);
      console.log(`🍃 [Final Recipes Debug] Input data logged for ${timeSlot}`);
    }

    // Get prompt manager and load configuration
    console.log('[Streaming API] Getting prompt manager');
    let promptManager;
    try {
      promptManager = getPromptManager();
      console.log(`[Streaming API] Prompt manager instance created`);
    } catch (error) {
      console.error('[Streaming API] Failed to create prompt manager:', error);
      return createErrorResponse(
        `Failed to initialize prompt manager: ${error instanceof Error ? error.message : String(error)}`,
        500
      );
    }
    
    // Log the prompt manager instance and base path
    const promptsBasePath = (promptManager as any).promptsBasePath;
    console.log(`[Streaming API] Prompt manager base path: ${promptsBasePath}`);
    
    // Verify the prompts directory exists and is accessible
    try {
      await fs.access(promptsBasePath);
      console.log(`[Streaming API] Prompts directory exists: ${promptsBasePath}`);
      
      // List available prompts for debugging
      try {
        const availablePrompts = await (promptManager as any).getAvailablePrompts();
        console.log('[Streaming API] Available prompts:', availablePrompts);
        
        // Check if the requested prompt exists
        if (availablePrompts && !availablePrompts.includes(step)) {
          console.error(`[Streaming API] Requested prompt '${step}' not found in available prompts`);
        } else if (availablePrompts) {
          console.log(`[Streaming API] Found requested prompt '${step}' in available prompts`);
        }
      } catch (listError) {
        console.error('[Streaming API] Error listing available prompts:', listError);
        // Continue execution even if listing fails
      }
    } catch (accessError) {
      console.error(`[Streaming API] Cannot access prompts directory at ${promptsBasePath}:`, accessError);
      return createErrorResponse(
        `Prompts directory not accessible: ${accessError instanceof Error ? accessError.message : String(accessError)}`,
        500
      );
    }
    
    const templateVariables = prepareTemplateVariables(feature, data);
    
    // Special handling for oil-enrichment step
    if (step === 'oil-enrichment') {
      console.log('[Streaming API] Processing oil-enrichment step with data:', {
        property_id: templateVariables['property_id'] || 'not set',
        property_name: templateVariables['property_name'] || 'not set',
        suggested_oils_count: Array.isArray(templateVariables['suggested_oils']) 
          ? templateVariables['suggested_oils'].length 
          : 'not an array'
      });
    }
    // Special handling for suggested-oils step to ensure property ID preservation
    else if (step === 'suggested-oils') {
      const therapeuticProperty = templateVariables['therapeutic_property'] || data.therapeutic_property;
      if (therapeuticProperty && therapeuticProperty.property_id) {
        console.log('[Streaming API] Adding property ID preservation instruction for suggested-oils');
        // Add a special instruction to preserve the exact property ID
        const propertyId = therapeuticProperty.property_id;
        templateVariables['property_id_preservation'] = `CRITICAL: You MUST preserve the exact property_id "${propertyId}" in your response. Do not modify, regenerate, or change this ID in any way.`;
      }
    }
    
    console.log('[Streaming API] Template variables prepared', templateVariables);

    // Load prompt configuration
    console.log('[Streaming API] Loading prompt configuration for step:', step);
    const { prompt, systemInstructions, userMessage, config } = await promptManager.getProcessedPrompt(step, templateVariables);
    console.log('[Streaming API] System instructions loaded, length:', systemInstructions.length);
    console.log('[Streaming API] User message loaded, length:', userMessage.length);
    console.log('[Streaming API] Config loaded:', { model: config.config.model, hasSchema: !!config.schema });
    
    // Use timeout from prompt config if available, otherwise use default
    const promptTimeout = config.timeout_seconds ? config.timeout_seconds * 1000 : API_TIMEOUT;
    console.log('[Streaming API] Using timeout:', promptTimeout, 'ms', config.timeout_seconds ? `(from prompt config: ${config.timeout_seconds}s)` : '(default)');

    // Create AI agent with structured output
    console.log('[Streaming API] Creating AI agent with JSON schema');

    // Configure tools based on the step
    let agentTools: any[] = [];
    if (step === 'suggested-oils') {
      agentTools = suggestedOilsSearchTools;
      console.log('[Streaming API] Adding suggested oils search tools for suggested-oils step');
    } else if (step === 'oil-enrichment') {
      // Import the supabaseOilSearchTool dynamically to avoid circular dependencies
      const { supabaseOilSearchTool } = await import('@/lib/ai/tools/supabase-oil-search-tool');
      agentTools = [supabaseOilSearchTool];
      console.log('[Streaming API] Adding supabase oil search tool for oil-enrichment step');
    }

    // Log the schema being used for debugging
    const schemaForAgent = config.schema as any;
    console.log(`[Streaming API] Schema for ${step}:`, JSON.stringify({
      schemaType: schemaForAgent?.type,
      schemaName: schemaForAgent?.name,
      strict: schemaForAgent?.strict,
      schemaKeys: schemaForAgent?.schema ? Object.keys(schemaForAgent.schema) : 'no schema',
      // Log a sample of the schema structure (first level only to avoid huge logs)
      schemaSample: schemaForAgent?.schema ? 
        Object.entries(schemaForAgent.schema).reduce((acc, [key, value]) => ({
          ...acc, 
          [key]: value && typeof value === 'object' ? 
            (Array.isArray(value) ? '[...array]' : '{...}') : 
            value
        }), {}) : 
        'no schema'
    }, null, 2));
    
    const agent = new Agent({
      name: `${feature}-${step}-agent`,
      instructions: systemInstructions,
      model: config.config.model || 'gpt-4o-mini',
      outputType: schemaForAgent, // Use the JSON schema from YAML
      tools: agentTools.length > 0 ? agentTools : undefined
    });
    console.log(`[Streaming API] Agent created with structured output and ${agentTools.length} tools`);
    console.log(`[Streaming API] System instructions: ${systemInstructions.substring(0, 200)}...`);
    console.log(`[Streaming API] User message: ${userMessage.substring(0, 200)}...`);

    // Run agent with streaming enabled
    console.log('[Streaming API] Starting agent execution with streaming');
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Request timeout')), promptTimeout);
    });

    // Use user() function to create proper user message and pass as array
    const userMessageItem = user(userMessage);

    let result: any;
    try {
      // Wrap agent execution with tracing for final-recipes step
      if (step === 'final-recipes') {
        const timeSlot = (data as any)?.timeSlot || 'unknown';
        const traceName = `Final Recipes Generation - ${timeSlot.charAt(0).toUpperCase() + timeSlot.slice(1)}`;

        console.log(`🍃 [Final Recipes Tracing] Starting traced execution for ${timeSlot}: ${traceName}`);

        const agentPromise = withTrace(traceName, async () => {
          console.log(`🍃 [Final Recipes Tracing] Inside trace wrapper for ${timeSlot}`);
          return await run(agent, [userMessageItem], { stream: true });
        });

        result = await Promise.race([agentPromise, timeoutPromise]);
        console.log(`🍃 [Final Recipes Tracing] Traced execution completed for ${timeSlot}`);
      } else {
        // Standard execution for other steps
        const agentPromise = run(agent, [userMessageItem], { stream: true });
        result = await Promise.race([agentPromise, timeoutPromise]);
      }

      console.log('[Streaming API] Agent execution started');
      logger.writeLog('Agent execution started');

      // All steps now use real-time streaming (including tool-based steps)
      console.log('[Streaming API] Starting real-time streaming for step:', step);
      logger.writeLog(`Starting real-time streaming for step: ${step}`);

    } catch (error) {
      console.log('[Streaming API] Agent execution failed', error);
      if (step === 'final-recipes') {
        const timeSlot = (data as any)?.timeSlot || 'unknown';
        console.error(`❌ [Final Recipes Tracing] Execution failed for ${timeSlot}:`, error);
      }
      if (error instanceof Error && error.message.includes('timeout')) {
        throw error; // Re-throw timeout errors to be handled in the catch block
      }
      throw error;
    }

    // Create SSE response with structured or text streaming
    console.log('[Streaming API] Creating SSE stream');
    const encoder = new TextEncoder();

    // Detect if we have structured output (JSON schema)
    const hasStructuredOutput = config.schema && config.schema['schema'];
    console.log('[Streaming API] Structured output detected:', hasStructuredOutput);
    console.log('[Streaming API] Config schema keys:', config.schema ? Object.keys(config.schema) : 'no schema');

    // Determine streaming mode (default to 'structured' if not specified)
    let finalStreamingMode = 'structured';
    if (['structured', 'text'].includes(streamingMode)) {
      finalStreamingMode = streamingMode;
    }
    console.log('[Streaming API] Final streaming mode:', finalStreamingMode);

    const stream = new ReadableStream({
      async start(controller) {
        const streamStartTime = Date.now();
        let streamingMode = 'unknown';

        try {
          // Use structured streaming for all steps with structured output
          if (finalStreamingMode === 'structured' && hasStructuredOutput) {
            streamingMode = 'structured';
            console.log('[Streaming API] 🚀 Starting structured streaming');
            await handleStructuredOnlyStreaming(result, controller, encoder, logger);
          } else {
            // Fall back to text mode if structured output is not available or text mode is explicitly requested
            streamingMode = 'text';
            console.log('[Streaming API] 🚀 Starting text streaming');
            await handleTextStreaming(result, controller, encoder);
          }

          const streamDuration = Date.now() - streamStartTime;
          console.log('[Streaming API] ✅ Stream completed successfully:', {
            mode: streamingMode,
            duration: streamDuration,
            traceId
          });

          // Log final output for final-recipes step
          if (step === 'final-recipes' && finalRecipesOutputLogger && result) {
            const timeSlot = (data as any)?.timeSlot || 'unknown';
            console.log(`🍃 [Final Recipes Debug] Logging output data for ${timeSlot}`);

            const outputData = {
              metadata: {
                step: 'final-recipes',
                timeSlot,
                timestamp: new Date().toISOString(),
                traceId,
                streamDuration,
                streamingMode
              },
              agentResult: {
                hasNewItems: !!result.newItems,
                newItemsCount: result.newItems?.length || 0,
                hasFinalOutput: !!result.finalOutput,
                finalOutputKeys: result.finalOutput ? Object.keys(result.finalOutput) : [],
                finalOutput: result.finalOutput
              }
            };

            await finalRecipesOutputLogger.log(outputData);
            console.log(`🍃 [Final Recipes Debug] Output data logged for ${timeSlot}`);
          }

        } catch (error) {
          const streamDuration = Date.now() - streamStartTime;
          console.error('[Streaming API] ❌ Stream error:', {
            error: error instanceof Error ? error.message : 'Unknown error',
            mode: streamingMode,
            duration: streamDuration,
            traceId
          });

          // Send comprehensive error event
          try {
            const errorEvent = `data: ${JSON.stringify({
              type: 'error',
              error: error instanceof Error ? error.message : 'Unknown streaming error',
              mode: streamingMode,
              duration: streamDuration,
              traceId,
              timestamp: new Date().toISOString(),
              recovery: 'Stream terminated due to error. Please try again.'
            })}

`;
            controller.enqueue(encoder.encode(errorEvent));
          } catch (errorEventError) {
            console.error('[Streaming API] ❌ Failed to send error event:', errorEventError);
          }
        } finally {
          try {
            controller.close();
            console.log('[Streaming API] 🔒 Stream controller closed');
            logger.close();
          } catch (closeError) {
            console.error('[Streaming API] ❌ Error closing stream controller:', closeError);
          }
        }
      }
    });

    return new NextResponse(stream, {
      status: 200,
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
      }
    });

  } catch (error) {
    const totalDuration = Date.now() - requestStartTime;

    // Handle prompt manager errors
    if (error instanceof PromptManagerError) {
      return createErrorResponse(
        `Failed to load AI configuration: ${error.message}`,
        500
      );
    }

    // Handle timeout errors
    if (error instanceof Error && error.message.includes('timeout')) {
      return createErrorResponse(
        'AI analysis took too long. Please try again.',
        408
      );
    }

    console.error('Error in streaming API:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return createErrorResponse(
      `An error occurred while processing your request: ${errorMessage}`,
      500
    );
  }
}

/**
 * GET handler - returns API status and configuration
 */
export async function GET(): Promise<NextResponse> {
  try {
    const hasApiKey = !!process.env['OPENAI_API_KEY'];

    return createSuccessResponse({
      status: 'healthy',
      service: 'AI Streaming API',
      version: '1.0.0',
      configured: hasApiKey,
      features: ['create-recipe'],
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return createErrorResponse('Health check failed', 500);
  }
}
