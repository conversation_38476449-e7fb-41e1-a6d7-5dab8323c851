# Troubleshooting Guide

## Overview

This guide helps developers troubleshoot common issues with the Create Recipe AI streaming system. Based on real implementation experiences and recent critical fixes, this guide covers the most important issues and their solutions.

**🎯 Current Status**: Updated to reflect production-ready implementation with resolved critical issues.

## 🚨 Critical Issues - Most Common Problems

### **1. Progress Tracking Issues** ✅ RESOLVED
**Symptoms**: Progress counter doesn't update incrementally, only shows final result  
**Root Cause**: JSON.stringify comparison preventing state updates  
**Solution**: Remove comparison, always update properties when results come in  
**Impact**: Real-time progress updates with immediate feedback

### **2. Expandable Row Logic Issues** ✅ RESOLVED
**Symptoms**: Clicking expand triggers additional API calls instead of showing loaded oils  
**Root Cause**: Expansion logic making unnecessary network requests  
**Solution**: Expansion only toggles visibility, no API calls  
**Impact**: Instant expand/collapse with optimized UX

### **3. Oil Mapping Issues** ✅ RESOLVED
**Symptoms**: Essential oils not correctly mapped to their respective properties  
**Root Cause**: Incorrect data structure mapping path  
**Solution**: Fixed mapping to use correct path: `result.property_oil_suggestion?.suggested_oils`  
**Impact**: Correct oil display in UI components

### **4. TypeScript Compliance Issues** ✅ RESOLVED
**Symptoms**: Multiple TS errors (implicit any, unused imports, JSX syntax)  
**Root Cause**: Missing type annotations and incorrect syntax  
**Solution**: Added explicit types, removed unused imports, fixed JSX comments  
**Impact**: Strict TypeScript compliance with no compilation errors

### **5. Results Population Issues** ✅ RESOLVED
**Symptoms**: Results Map not populated correctly, preventing UI updates  
**Root Cause**: Streaming state updates not properly handling results  
**Solution**: Fixed streaming state updates with proper results handling  
**Impact**: Reliable UI updates when streaming completes

### **6. Oil Enrichment Template Variables Not Populated** ✅ RESOLVED
**Symptoms**: Template variables in `oil-enrichment.yaml` showing empty values like `health_concern:`, `gender:`, etc.  
**Root Cause**: Multiple issues in the enrichment data flow  
**Solution**: Fixed template variable population, context passing, and response parsing  
**Impact**: Proper template variable substitution and successful oil enrichment

**Detailed Issues & Solutions:**

#### **Issue 1: Template Variable Population**
**Problem**: `startOilEnrichmentStreaming` wasn't using `createStreamRequest()` utility
```typescript
// ❌ WRONG - Manual object creation missing context
const requestData = {
  feature: 'create-recipe',
  step: 'oil-enrichment',
  dataKeys: ['property_id', 'suggested_oils']
};

// ✅ CORRECT - Use createStreamRequest for proper template variables
const requestData = createStreamRequest(
  'oil-enrichment',
  { healthConcern, demographics, selectedCauses, selectedSymptoms, suggested_oils },
  userLanguage
);
```

#### **Issue 2: Missing Context Data**
**Problem**: Enrichment function wasn't receiving required context parameters
```typescript
// ❌ WRONG - Missing context parameters
const startOilEnrichmentStreaming = async (propertySuggestions, userLanguage) => {
  // No access to health concern, demographics, causes, symptoms
};

// ✅ CORRECT - Pass all required context
const startOilEnrichmentStreaming = async (
  propertySuggestions, 
  healthConcern, 
  demographics, 
  selectedCauses, 
  selectedSymptoms, 
  userLanguage
) => {
  // Full context available for template variables
};
```

#### **Issue 3: Response Parser Path**
**Problem**: Parser looking for wrong data structure path
```typescript
// ❌ WRONG - Incorrect nested path
if (updates.finalData?.output?.data?.enriched_oils) {
  return updates.finalData.output.data.enriched_oils;
}

// ✅ CORRECT - Actual response structure
if (updates.finalData?.data?.enriched_oils) {
  return updates.finalData.data.enriched_oils;
}
```

#### **Issue 4: Component Context Access**
**Problem**: Component not accessing required context from store
```typescript
// ❌ WRONG - Missing context data
const { therapeuticProperties } = useRecipeStore();
const enrichOils = () => {
  startOilEnrichmentStreaming(propertySuggestions, userLanguage);
};

// ✅ CORRECT - Access all required context
const { 
  therapeuticProperties, 
  healthConcern, 
  demographics, 
  selectedCauses, 
  selectedSymptoms 
} = useRecipeStore();

const enrichOils = () => {
  // Safety checks for required data
  if (!healthConcern?.healthConcern || !demographics?.gender) {
    console.error('Missing required context for enrichment');
    return;
  }
  
  startOilEnrichmentStreaming(
    propertySuggestions, 
    healthConcern, 
    demographics, 
    selectedCauses, 
    selectedSymptoms, 
    userLanguage
  );
};
```

#### **Debugging Oil Enrichment Issues**
```typescript
// 1. Check template variable population
console.log('🔍 Template variables being sent:', {
  health_concern: healthConcern?.healthConcern,
  gender: demographics?.gender,
  age_category: demographics?.ageCategory,
  selected_causes: selectedCauses,
  selected_symptoms: selectedSymptoms,
  suggested_oils: propertySuggestions
});

// 2. Verify createStreamRequest output
const requestData = createStreamRequest('oil-enrichment', context, userLanguage);
console.log('🔍 Request data structure:', requestData);

// 3. Check AI response structure
console.log('🔍 AI response structure:', {
  finalData: updates.finalData,
  hasData: !!updates.finalData?.data,
  hasEnrichedOils: !!updates.finalData?.data?.enriched_oils,
  enrichedOilsCount: updates.finalData?.data?.enriched_oils?.length
});

// 4. Verify context availability in component
console.log('🔍 Component context check:', {
  hasHealthConcern: !!healthConcern?.healthConcern,
  hasDemographics: !!demographics?.gender,
  hasCauses: selectedCauses?.length > 0,
  hasSymptoms: selectedSymptoms?.length > 0
});
```

## 🔧 Common Development Issues

### **Button Click Doesn't Trigger Streaming**

**Symptoms:**
- Button appears to work but nothing happens
- No console logs or network requests
- Modal never opens
- No errors shown

**Root Cause:** React Hook Form + local state management conflict

**Solution:**
```typescript
// ❌ WRONG - This combination causes silent failures
const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());
const { handleSubmit } = useForm();
const onSubmit = async (data: any) => { /* never called */ };
<form onSubmit={handleSubmit(onSubmit)}>

// ✅ CORRECT - Choose ONE approach consistently
const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());
const onSubmit = async () => { /* direct function */ };
<button type="button" onClick={onSubmit}>Continue</button>
```

### **Modal Shows Items Only at Completion**

**Symptoms:**
- Backend logs show progressive items
- Frontend modal only shows items when streaming completes
- No progressive display during streaming

**Root Cause:** Mixed state management patterns

**Solution:**
```typescript
// ❌ WRONG - Mixed patterns cause display issues
const [isModalOpen, setIsModalOpen] = useState(false); // Local state
const { isStreamingOils } = useRecipeStore(); // Store state
<AIStreamingModal isOpen={isStreamingOils} /> // Using store state

// ✅ CORRECT - Hook-based pattern (consistent)
const { isStreaming } = useAIStreaming();
<AIStreamingModal isOpen={isStreaming} />
```

### **Streaming Not Starting**

**Symptoms:**
- Loading indicator shows but no data appears
- Console shows "Failed to start streaming" errors
- Component remains in loading state indefinitely

**Solutions:**

1. **Check Required Data:**
```typescript
// Verify health concern exists
console.log('Health concern:', healthConcern);
if (!healthConcern?.healthConcern) {
  console.error('Health concern is missing');
}

// Verify demographics format
console.log('Demographics:', demographics);
const requiredFields = ['gender', 'ageCategory', 'specificAge'];
const missingFields = requiredFields.filter(field => !demographics?.[field]);
if (missingFields.length > 0) {
  console.error('Missing demographics fields:', missingFields);
}
```

2. **Check Network Connectivity:**
```typescript
// Test API endpoint
try {
  const response = await fetch('/api/health/streaming');
  if (!response.ok) {
    console.error('API endpoint not available');
  }
} catch (error) {
  console.error('Network error:', error);
}
```

3. **Verify Step Configuration:**
```typescript
import { getStepConfig } from '../config/step-mapping';

const config = getStepConfig('potential-causes');
if (!config) {
  console.error('Step configuration not found');
} else {
  console.log('Step config:', config);
}
```

### **Partial Data Not Updating**

**Symptoms:**
- Streaming starts but UI doesn't update with partial data
- Console shows data being received but components don't re-render
- Final data appears but progressive updates missing

**Solutions:**

1. **Check useEffect Dependencies:**
```typescript
// Ensure proper dependencies
useEffect(() => {
  if (partialData && Array.isArray(partialData)) {
    console.log('Processing partial data:', partialData);
    setStepData(partialData);
  }
}, [partialData]); // Make sure partialData is in dependencies
```

2. **Verify Data Transformation:**
```typescript
// Check if transformation is working
useEffect(() => {
  if (partialData) {
    console.log('Raw partial data:', partialData);
    const transformed = transformData(partialData, stepId);
    console.log('Transformed data:', transformed);
    setStepData(transformed);
  }
}, [partialData, stepId]);
```

## 🐛 Debug Infrastructure

### **Comprehensive Logging**

The system now includes comprehensive debug logging for troubleshooting:

```typescript
// Properties Display logging
console.log('[Properties Display] Property IDs sent:', propertyIds);
console.log('[Properties Display] Results received:', results);
console.log('[Properties Display] Mapping status:', mappingStatus);

// Therapeutic Properties logging
console.log('[Therapeutic Properties] Loading state:', loadingStates);
console.log('[Therapeutic Properties] Expand/collapse action:', action);
console.log('[Therapeutic Properties] Oil mapping result:', mappedOils);

// Parallel Streaming logging
console.log(`[Parallel Streaming] Setting result for property ${propertyId}:`, result);
```

### **Debug Patterns**

#### **Raw AI Response Logging**
```typescript
// ✅ CRITICAL - Log raw streaming data to see exactly what AI returns
useEffect(() => {
  if (propertiesPartialData && Array.isArray(propertiesPartialData)) {
    console.log('📥 RAW PROPERTIES PARTIAL DATA:', propertiesPartialData);
    // Process partial data...
  }
}, [propertiesPartialData]);

useEffect(() => {
  if (isComplete && finalData) {
    console.log('✅ RAW PROPERTIES FINAL DATA:', finalData);
    // Process final data...
  }
}, [isComplete, finalData]);
```

#### **Field Mapping Analysis**
```typescript
// ✅ CRITICAL - Show ALL fields in AI response to identify missing mappings
const transformedProperties = propertiesPartialData.map((property, index) => {
  console.log(`🔄 Transforming property ${index}:`, {
    original: property,
    allOriginalFields: Object.keys(property),
    fullOriginalProperty: property,
    // Specific field checks
    relevancy_score: property.relevancy_score,
    addresses_cause_ids: property.addresses_cause_ids,
    addresses_symptom_ids: property.addresses_symptom_ids
  });

  return {
    // Preserve ALL AI response fields
    property_id: property.property_id,
    relevancy_score: property.relevancy_score, // Keep original
    relevancy: property.relevancy_score, // Map for compatibility
    addresses_cause_ids: property.addresses_cause_ids || [],
    addresses_symptom_ids: property.addresses_symptom_ids || []
  };
});
```

## 🔍 Performance Issues

### **Navigation Delays**

**Symptoms:**
- Navigation between steps taking 30+ seconds
- Multiple "Fast Refresh rebuilding" messages
- High CPU usage during development

**Solutions:**

1. **Check Development Configuration:**
```typescript
// Ensure Turbopack is enabled
// next.config.ts
...(process.env.NODE_ENV === 'development' && {
  turbopack: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },
})
```

2. **Disable Sentry in Development:**
```typescript
// Only enable Sentry in production
export default process.env.NODE_ENV === 'production' 
  ? withSentryConfig(nextConfig, sentryWebpackPluginOptions)
  : nextConfig;
```

3. **Clear Next.js Cache:**
```bash
rm -rf .next
npm run dev
```

### **Multiple Re-renders**

**Symptoms:**
- Multiple GET requests to next step page after AI streaming completes
- Excessive component re-renders

**Solution:**
```typescript
// Consolidate state updates and remove setTimeout delays
// Only sync if the URL step is different from store AND we're not already navigating
if (currentStep && currentStep !== storeCurrentStep && !isLoading) {
  setCurrentStep(currentStep);
}
```

## 🚀 Best Practices

### **State Management**
- **Use consistent patterns**: Either store-based OR hook-based, never mix
- **Avoid React Hook Form conflicts**: Use direct button onClick for streaming steps
- **Proper timeout configuration**: 60s+ for complex analysis
- **Real-time progress updates**: Always update state when results come in

### **Error Handling**
- **Comprehensive logging**: Log all critical data flow points
- **Graceful degradation**: Continue processing even if some items fail
- **Retry logic**: Implement exponential backoff for network failures
- **User feedback**: Provide clear error messages and recovery options

### **Performance**
- **Parallel processing**: Use parallel streaming for multiple items
- **Resource optimization**: Efficient memory and network usage
- **Development optimization**: Disable heavy tools in development mode
- **Caching**: Implement appropriate caching strategies

## 📝 Implementation Notes

### **Current Working Patterns**

#### **Store-Based Pattern (Complex Steps)**
```typescript
const { isStreamingProperties, setStreamingProperties } = useRecipeStore();
<AIStreamingModal isOpen={isStreamingProperties} />
```

#### **Hook-Based Pattern (Simple Steps)**
```typescript
const { isStreaming } = useAIStreaming();
<AIStreamingModal isOpen={isStreaming} />
```

#### **Parallel Processing**
```typescript
const { startParallelStreaming } = useAIParallelStreaming();
await startParallelStreaming(therapeuticProperties, ...);
```

### **Critical Success Factors**
- **Consistent state management**: Never mix patterns
- **Proper timeout configuration**: Match analysis complexity
- **Comprehensive logging**: Debug all data flow points
- **Real-time updates**: Progressive data display during streaming
- **Error resilience**: Graceful handling of failures

## 🔗 Related Documentation

- [AI Streaming Architecture](./ai-streaming-architecture.md) - Complete architecture overview
- [Development Guide](./development-guide.md) - Step-by-step implementation guide
- [Performance Optimization](./performance-optimization.md) - Performance tuning guide
