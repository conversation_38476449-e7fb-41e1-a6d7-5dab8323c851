import { useCallback } from 'react';
import { useParallelStreamingEngine, ParallelStreamRequest } from '@/lib/ai/hooks/use-parallel-streaming-engine';
import { createStreamRequest } from '../utils/api-data-transform';
import type {
  TherapeuticProperty,
  PropertyOilSuggestions,
  HealthConcernData,
  DemographicsData,
  PotentialCause,
  PotentialSymptom,
  RecipeTimeSlot
} from '../types/recipe.types';
import { DEFAULT_API_LANGUAGE } from '../constants/recipe.constants';

export function useCreateRecipeStreaming() {
  const { streamingState, startStreams, resetState } = useParallelStreamingEngine<any>();

  const startOilSuggestionStreaming = useCallback(async (
    properties: TherapeuticProperty[],
    healthConcern: HealthConcernData,
    demographics: DemographicsData,
    selectedCauses: PotentialCause[],
    selectedSymptoms: PotentialSymptom[],
    userLanguage: string = DEFAULT_API_LANGUAGE
  ) => {
    const requests: ParallelStreamRequest[] = properties.map(property => {
      const requestData = createStreamRequest(
        'create-recipe',
        'suggested-oils',
        healthConcern,
        demographics,
        selectedCauses,
        selectedSymptoms,
        userLanguage,
        property
      );
      
      // Creating oil suggestion request for property
      
      return {
        id: property.property_id,
        url: '/api/ai/streaming',
        requestData,
        label: property.property_name,
        responseParser: (updates) => {
          // Updated for new schema: suggested_oils is now directly in data
          if (updates.finalData?.data?.suggested_oils) {
            return {
              therapeutic_property_context: updates.finalData.data.therapeutic_property_context,
              suggested_oils: updates.finalData.data.suggested_oils
            };
          }
          return null;
        }
      };
    });

    return await startStreams(requests);
  }, [startStreams]);

  const startOilEnrichmentStreaming = useCallback(async (
    propertySuggestions: PropertyOilSuggestions[],
    healthConcern: HealthConcernData,
    demographics: DemographicsData,
    selectedCauses: PotentialCause[],
    selectedSymptoms: PotentialSymptom[],
    userLanguage: string = DEFAULT_API_LANGUAGE
  ) => {
    const propertiesToEnrich = propertySuggestions.filter(
      suggestion => suggestion.suggested_oils && suggestion.suggested_oils.length > 0
    );

    console.log('🔍 [Oil Enrichment] Starting oil enrichment for properties:', propertiesToEnrich.length);

    const requests: ParallelStreamRequest[] = propertiesToEnrich.map(suggestion => ({
      id: suggestion.property_id,
      url: '/api/ai/streaming',
      requestData: createStreamRequest(
        'create-recipe',
        'oil-enrichment',
        healthConcern,
        demographics,
        selectedCauses,
        selectedSymptoms,
        userLanguage,
        {
          property_id: suggestion.property_id,
          property_name: suggestion.property_name,
          property_name_english: suggestion.property_name_in_english || suggestion.property_name,
          description: suggestion.description,
          suggested_oils: suggestion.suggested_oils || []
        } as TherapeuticProperty
      ),
      label: suggestion.property_name,
      responseParser: (updates) => {
        console.log('🔍 [Oil Enrichment] Response parser received updates for:', suggestion.property_name, updates);
        console.log('🔍 [Oil Enrichment] Full updates structure:', JSON.stringify(updates, null, 2));
        
        if (updates.finalData?.data?.enriched_oils) {
          console.log('✅ [Oil Enrichment] Successfully parsed enriched oils:', updates.finalData.data.enriched_oils.length, 'oils');
          
          // Validate therapeutic property context is present
          if (updates.finalData.data.therapeutic_property_context) {
            console.log('✅ [Oil Enrichment] Therapeutic property context found:', updates.finalData.data.therapeutic_property_context.property_id);
          } else {
            console.warn('⚠️ [Oil Enrichment] Missing therapeutic property context in response');
          }
          
          return updates.finalData.data.enriched_oils;
        }
        
        console.log('❌ [Oil Enrichment] No enriched oils found in response structure');
        console.log('❌ [Oil Enrichment] finalData keys:', Object.keys(updates.finalData || {}));
        if (updates.finalData?.data) {
          console.log('❌ [Oil Enrichment] data keys:', Object.keys(updates.finalData.data || {}));
        }
        return null;
      }
    }));

    const results = await startStreams(requests);
    console.log('✅ [Oil Enrichment] Final enrichment results (Map):', results);
    console.log('✅ [Oil Enrichment] Results size:', results.size);
    console.log('✅ [Oil Enrichment] Results keys:', Array.from(results.keys()));
    console.log('✅ [Oil Enrichment] Results values preview:', Array.from(results.values()).map(v => Array.isArray(v) ? `Array(${v.length})` : typeof v));
    
    return results;
  }, [startStreams]);

  const startFinalRecipesStreaming = useCallback(async (
    timeSlots: RecipeTimeSlot[],
    healthConcern: HealthConcernData,
    demographics: DemographicsData,
    selectedCauses: PotentialCause[],
    selectedSymptoms: PotentialSymptom[],
    suggestedOils: PropertyOilSuggestions[],
    userLanguage: string = DEFAULT_API_LANGUAGE
  ) => {
    console.log('🍃 [Final Recipes] Starting recipe generation for time slots:', timeSlots);
    console.log('🔍 [Final Recipes] Input validation:', {
      timeSlots: timeSlots.length,
      hasHealthConcern: !!healthConcern,
      hasDemographics: !!demographics,
      selectedCausesCount: selectedCauses.length,
      selectedSymptomsCount: selectedSymptoms.length,
      suggestedOilsCount: suggestedOils.length,
      userLanguage
    });

    // Log detailed input data for each time slot generation
    console.log('🔍 [Final Recipes] Detailed input data:', {
      healthConcern: healthConcern.healthConcern,
      demographics: {
        gender: demographics.gender,
        ageCategory: demographics.ageCategory,
        specificAge: demographics.specificAge
      },
      selectedCauses: selectedCauses.map(c => ({
        id: c.cause_id,
        name: c.cause_name
      })),
      selectedSymptoms: selectedSymptoms.map(s => ({
        id: s.symptom_id,
        name: s.symptom_name
      })),
      suggestedOilsByProperty: suggestedOils.map(prop => ({
        propertyId: prop.property_id,
        propertyName: prop.property_name,
        oilsCount: prop.suggested_oils?.length || 0
      }))
    });

    const startTime = Date.now();
    const requests: ParallelStreamRequest[] = timeSlots.map(timeSlot => {
      console.log(`🔧 [Final Recipes] Creating request for ${timeSlot} time slot`);

      const requestData = createStreamRequest(
        'create-recipe',
        'final-recipes',
        healthConcern,
        demographics,
        selectedCauses,
        selectedSymptoms,
        userLanguage,
        undefined, // No property for final recipes
        { timeSlot, suggestedOils } // Additional data for final recipes
      );

      console.log(`🔍 [Final Recipes] Request data for ${timeSlot}:`, {
        feature: requestData.feature,
        step: requestData.step,
        timeSlot: timeSlot,
        dataKeys: Object.keys(requestData.data || {}),
        hasAdditionalData: !!(requestData.data as any)?.timeSlot
      });

      return {
        id: timeSlot,
        url: '/api/ai/streaming',
        requestData,
        label: `${timeSlot} recipe`,
        responseParser: (updates) => {
          console.log(`🔍 [Final Recipes] Response parser called for ${timeSlot}:`, {
            hasFinalData: !!updates.finalData,
            hasData: !!updates.finalData?.data,
            hasRecipeProtocol: !!updates.finalData?.data?.recipe_protocol,
            updateKeys: Object.keys(updates || {})
          });

          // Extract recipe protocol from the response
          if (updates.finalData?.data?.recipe_protocol) {
            const recipe = updates.finalData.data.recipe_protocol;
            console.log(`✅ [Final Recipes] Successfully parsed ${timeSlot} recipe:`, {
              recipeName: recipe.recipe_name_localized,
              selectedOilsCount: recipe.selected_oils?.length || 0,
              preparationStepsCount: recipe.preparation_steps?.length || 0,
              usageInstructionsCount: recipe.usage_instructions?.length || 0,
              hasContainerInfo: !!recipe.container_info
            });
            return recipe;
          }

          console.log(`❌ [Final Recipes] No recipe protocol found for ${timeSlot}`);
          console.log(`❌ [Final Recipes] Available data keys for ${timeSlot}:`,
            updates.finalData?.data ? Object.keys(updates.finalData.data) : 'No data object');
          return null;
        }
      };
    });

    console.log(`🍃 [Final Recipes] Initiating ${requests.length} parallel streaming requests`);

    // Enhanced error tracking with promise monitoring
    const promiseTracker = new Map<string, { status: 'pending' | 'resolved' | 'rejected' | 'timeout', startTime: number, error?: any }>();

    // Initialize tracking for each time slot
    timeSlots.forEach(slot => {
      promiseTracker.set(slot, { status: 'pending', startTime: Date.now() });
    });

    try {
      // Monitor promise states during execution
      const monitoringInterval = setInterval(() => {
        console.log('🔍 [Final Recipes] Promise status check:', {
          timestamp: new Date().toISOString(),
          promises: Array.from(promiseTracker.entries()).map(([slot, info]) => ({
            slot,
            status: info.status,
            durationMs: Date.now() - info.startTime
          }))
        });
      }, 5000); // Check every 5 seconds

      // Set up timeout detection for individual promises
      const INDIVIDUAL_TIMEOUT = 45000; // 45 seconds per recipe
      const timeoutPromises = timeSlots.map(slot =>
        new Promise((_, reject) => {
          setTimeout(() => {
            promiseTracker.set(slot, { ...promiseTracker.get(slot)!, status: 'timeout' });
            console.error(`❌ [Final Recipes] Timeout detected for ${slot} after ${INDIVIDUAL_TIMEOUT}ms`);
            reject(new Error(`Timeout: ${slot} recipe generation exceeded ${INDIVIDUAL_TIMEOUT}ms`));
          }, INDIVIDUAL_TIMEOUT);
        })
      );

      const results = await Promise.race([
        startStreams(requests).then(results => {
          clearInterval(monitoringInterval);

          // Update promise tracker with results
          results.forEach((recipe, timeSlot) => {
            const currentInfo = promiseTracker.get(timeSlot);
            if (currentInfo) {
              promiseTracker.set(timeSlot, {
                ...currentInfo,
                status: recipe ? 'resolved' : 'rejected',
                error: recipe ? undefined : 'No recipe returned'
              });
            }
          });

          return results;
        }),
        Promise.allSettled(timeoutPromises).then(() => {
          clearInterval(monitoringInterval);
          throw new Error('One or more recipe generations timed out');
        })
      ]);

      const endTime = Date.now();

      console.log('✅ [Final Recipes] All recipe generation completed');
      console.log('🔍 [Final Recipes] Generation results:', {
        totalDurationMs: endTime - startTime,
        totalDurationSeconds: Math.round((endTime - startTime) / 1000),
        resultsCount: results.size,
        expectedCount: timeSlots.length,
        successfulSlots: Array.from(results.entries()).filter(([_, recipe]) => recipe !== null).map(([slot, _]) => slot),
        failedSlots: Array.from(results.entries()).filter(([_, recipe]) => recipe === null).map(([slot, _]) => slot)
      });

      // Log detailed promise tracking results
      console.log('🔍 [Final Recipes] Promise tracking summary:', {
        finalStates: Array.from(promiseTracker.entries()).map(([slot, info]) => ({
          slot,
          status: info.status,
          totalDurationMs: endTime - info.startTime,
          hadError: !!info.error,
          errorMessage: info.error
        }))
      });

      // Log individual results with error details
      results.forEach((recipe, timeSlot) => {
        const trackingInfo = promiseTracker.get(timeSlot);
        if (recipe) {
          console.log(`✅ [Final Recipes] ${timeSlot} recipe generation successful:`, {
            duration: trackingInfo ? Date.now() - trackingInfo.startTime : 'unknown',
            status: trackingInfo?.status || 'unknown'
          });
        } else {
          console.log(`❌ [Final Recipes] ${timeSlot} recipe generation failed:`, {
            duration: trackingInfo ? Date.now() - trackingInfo.startTime : 'unknown',
            status: trackingInfo?.status || 'unknown',
            error: trackingInfo?.error || 'Unknown failure'
          });
        }
      });

      return results;
    } catch (error) {
      const endTime = Date.now();

      // Enhanced error logging with promise state analysis
      console.error(`❌ [Final Recipes] Recipe generation failed after ${endTime - startTime}ms:`, error);
      console.error(`❌ [Final Recipes] Error details:`, {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        errorType: typeof error,
        isTimeoutError: error instanceof Error && error.message.includes('timeout'),
        isNetworkError: error instanceof Error && (error.message.includes('network') || error.message.includes('fetch')),
        isAPIError: error instanceof Error && error.message.includes('API')
      });

      // Log final promise states for debugging
      console.error(`❌ [Final Recipes] Promise states at failure:`, {
        promiseStates: Array.from(promiseTracker.entries()).map(([slot, info]) => ({
          slot,
          status: info.status,
          durationMs: endTime - info.startTime,
          hadError: !!info.error,
          errorMessage: info.error
        }))
      });

      // Provide specific error recovery suggestions
      if (error instanceof Error) {
        if (error.message.includes('timeout')) {
          console.error(`❌ [Final Recipes] Timeout recovery suggestions:`, {
            suggestion1: 'Check network connectivity',
            suggestion2: 'Verify OpenAI API status',
            suggestion3: 'Consider reducing input complexity',
            suggestion4: 'Retry with exponential backoff'
          });
        } else if (error.message.includes('API')) {
          console.error(`❌ [Final Recipes] API error recovery suggestions:`, {
            suggestion1: 'Check OpenAI API key validity',
            suggestion2: 'Verify API rate limits',
            suggestion3: 'Check API endpoint availability',
            suggestion4: 'Review request payload format'
          });
        }
      }

      throw error;
    }
  }, [startStreams]);

  return {
    streamingState,
    startOilSuggestionStreaming,
    startOilEnrichmentStreaming,
    startFinalRecipesStreaming,
    resetState,
  };
}
