/**
 * @fileoverview Protocol summary card component with flip animation
 * Shows overview of each recipe protocol with flip interaction
 */

'use client';

import React, { useState } from 'react';
import { RecipeTimeSlot, FinalRecipeProtocol } from '../types/recipe.types';
import { useI18n } from '@/hooks/use-i18n';

interface ProtocolSummaryCardProps {
  timeSlot: RecipeTimeSlot;
  recipe: FinalRecipeProtocol | null;
  onViewDetails: () => void;
}

/**
 * Protocol summary card with flip animation
 * Follows the exact design from standalone-v1.html flip cards
 */
export function ProtocolSummaryCard({ timeSlot, recipe, onViewDetails }: ProtocolSummaryCardProps) {
  const [isFlipped, setIsFlipped] = useState(false);
  const { t } = useI18n();

  const timeSlotConfig = {
    morning: {
      emoji: '🌅',
      label: t('create-recipe:steps.final-recipes.protocols.morning.subtitle'),
      gradient: 'from-slate-800 to-slate-900',
      shadowColor: 'shadow-slate-500/30',
      accentColor: 'text-teal-300',
      buttonColor: 'bg-white text-slate-900 hover:bg-slate-200',
      purpose: t('create-recipe:steps.final-recipes.protocols.morning.purpose')
    },
    'mid-day': {
      emoji: '☀️',
      label: t('create-recipe:steps.final-recipes.protocols.midDay.subtitle'),
      gradient: 'from-yellow-400 to-orange-500',
      shadowColor: 'shadow-orange-300/30',
      accentColor: 'text-yellow-100',
      buttonColor: 'bg-white text-orange-700 hover:bg-orange-100',
      purpose: t('create-recipe:steps.final-recipes.protocols.midDay.purpose')
    },
    night: {
      emoji: '🌙',
      label: t('create-recipe:steps.final-recipes.protocols.night.subtitle'),
      gradient: 'from-indigo-500 to-indigo-900',
      shadowColor: 'shadow-indigo-500/30',
      accentColor: 'text-indigo-100',
      buttonColor: 'bg-white text-indigo-700 hover:bg-indigo-100',
      purpose: t('create-recipe:steps.final-recipes.protocols.night.purpose')
    }
  };

  const config = timeSlotConfig[timeSlot];

  const handleFlip = () => {
    setIsFlipped(!isFlipped);
  };

  const handleViewRecipe = () => {
    onViewDetails();
  };

  if (!recipe) {
    return (
      <div className="h-96 bg-gray-100 rounded-2xl animate-pulse flex items-center justify-center">
        <div className="text-gray-500">{t('create-recipe:steps.final-recipes.loading')}</div>
      </div>
    );
  }

  return (
    <div className="h-96 perspective-1000">
      <div className={`
        relative w-full h-full transition-transform duration-800 transform-style-preserve-3d
        ${isFlipped ? 'rotate-y-180' : ''}
      `}>
        {/* Front of card */}
        <div className={`
          absolute w-full h-full backface-hidden rounded-2xl overflow-hidden
          bg-gradient-to-br ${config.gradient} p-8 flex flex-col items-center justify-between
          text-white shadow-2xl ${config.shadowColor}
        `}>
          <div className="w-full flex flex-col items-start">
            <span className={`text-sm uppercase tracking-widest ${config.accentColor}`}>
              PROTOCOLO
            </span>
            <div className="flex items-center gap-2 mt-2">
              <span className="text-2xl">{config.emoji}</span>
              <h3 className="text-2xl font-black">{config.label}</h3>
            </div>
          </div>
          
          <div className="flex-1 flex flex-col items-center justify-center w-full mt-4">
            <div className="text-center">
              <div className={`text-base ${config.accentColor}`}>Sinergia para</div>
              <div className="text-xl font-bold mb-6">{config.purpose}</div>
            </div>
            <button
              onClick={handleFlip}
              className={`
                inline-flex items-center gap-2 px-6 py-2.5 font-semibold rounded-full
                shadow-lg transition-all duration-200 ${config.buttonColor}
              `}
            >
              Ver Detalhes
              <svg xmlns='http://www.w3.org/2000/svg' className='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'>
                <path fillRule='evenodd' d='M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z' clipRule='evenodd'/>
              </svg>
            </button>
          </div>
          
          <div className={`text-center text-xs ${config.accentColor} mt-4 w-full`}>
            Clique no botão para ver os detalhes
          </div>
        </div>

        {/* Back of card */}
        <div className="absolute w-full h-full backface-hidden rotate-y-180 bg-white p-6 overflow-y-auto rounded-2xl border border-gray-200 shadow-lg">
          <button
            onClick={handleFlip}
            className="absolute top-4 right-4 text-slate-400 hover:text-teal-700 text-2xl leading-none rounded-full focus:outline-none focus:ring-2 focus:ring-teal-400"
            aria-label="Fechar"
          >
            &times;
          </button>
          
          <div className="mt-2">
            <div className="mb-2 text-sm text-gray-700">
              <strong>Objetivo:</strong> {recipe.description_localized}
            </div>
          </div>
          
          <div className="mt-4">
            <ul className="mt-2 space-y-1">
              {recipe.selected_oils.map((oil, index) => (
                <li key={index} className="flex justify-between items-center">
                  <span className="text-violet-600">● {oil.name_localized}</span>
                  <span className="font-mono text-sm">{oil.drops_count} gotas</span>
                </li>
              ))}
            </ul>
          </div>
          
          <div className="mt-4">
            <div className="mb-2 text-sm text-gray-700">
              <strong>Preparo rápido:</strong> Misture os óleos em um frasco roll-on de {recipe.container_recommendation.size_ml}ml e complete com {recipe.carrier_oil.name_localized}. Agite bem antes de usar.
            </div>
            <div className="mb-2 text-sm text-gray-700">
              <strong>Como Usar:</strong> {recipe.usage_instructions_localized[0] || recipe.application_method_localized}
            </div>
          </div>
          
          <div className="mt-2 text-right">
            <button
              onClick={handleViewRecipe}
              className={`text-sm hover:underline ${
                timeSlot === 'morning' ? 'text-teal-600 hover:text-teal-800' :
                timeSlot === 'mid-day' ? 'text-orange-600 hover:text-orange-800' :
                'text-indigo-600 hover:text-indigo-800'
              }`}
            >
              {t('create-recipe:steps.final-recipes.overview.protocolSummary.viewRecipe')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
